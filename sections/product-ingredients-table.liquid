{% comment %}
  Product Ingredients Table Section
  Displays a table of product ingredients from metaobjects
{% endcomment %}

{% liquid
  assign metafield_namespace = section.settings.metafield_namespace | default: 'custom.vitamin_table'

  # Try to access metafield
  assign ingredients_metafield = product.metafields[metafield_namespace]
  if ingredients_metafield == blank
    assign ingredients_metafield = product.metafields.custom.vitamin_table
  endif

  assign ingredients_list = ingredients_metafield.value

  assign field_1 = section.settings.field_1_name | default: 'vitamin_name'
  assign field_2 = section.settings.field_2_name | default: 'daily_men'
  assign field_3 = section.settings.field_3_name | default: 'daily_women'
  assign field_4 = section.settings.field_4_name | default: 'amount'
%}


{% style %}
  .product-ingredients-table-section {
    width: 100%;
    padding: 0;
    background: transparent;
    direction: rtl;
    padding: 0 20px;
  }

  .ingredients-table-container {
    background: #F8F8F5;
    border-radius: 30px;
    padding: {{ section.settings.padding_top }}px 120px {{ section.settings.padding_bottom }}px;
    margin: 0 auto;
  }

  @media screen and (max-width: 767px) {
    .product-ingredients-table-section {
       padding: 0px;
    }
    .ingredients-table-container {
      padding: {{ section.settings.padding_top_mobile }}px 0 {{ section.settings.padding_bottom_mobile }}px;
      border-radius: 0;
      margin: 0;
    }
  }

  .ingredients-table-wrapper {
    max-width: 100%;
  }

  .table-with-footer-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  @media screen and (max-width: 767px) {
    .table-with-footer-wrapper {
      margin: 0;
      padding: 0 20px;
    }
  }

  .ingredients-table-header {
    text-align: center;
    margin-bottom: 40px;
  }

  .ingredients-table-header h2 {
    font-size: {{ section.settings.heading_size }}px;
    font-weight: 700;
    color: {{ section.settings.heading_color }};
    margin: 0 0 16px;
    line-height: 1.2;
  }

  @media screen and (max-width: 767px) {
    .ingredients-table-header h2 {
      font-size: {{ section.settings.heading_size_mobile }}px;
    }
  }

  .ingredients-table-header p {
    font-size: {{ section.settings.description_size }}px;
    color: {{ section.settings.description_color }};
    margin: 0;
    line-height: 1.5;
  }

  @media screen and (max-width: 767px) {
    .ingredients-table-header p {
      font-size: {{ section.settings.description_size_mobile }}px;
    }
  }

  .ingredients-table {
    width: 100%;
    border-collapse: collapse;
    background: #FFFFFF;
    border-radius: {{ section.settings.table_border_radius }}px {{ section.settings.table_border_radius }}px 0 0;
    overflow: hidden;
    box-shadow: none;
  }

  .ingredients-table thead {
    background: #FFFFFF;
  }

  .ingredients-table thead th {
    padding: {{ section.settings.header_padding }}px;
    text-align: center;
    font-size: {{ section.settings.header_font_size }}px;
    font-weight: 700;
    color: {{ section.settings.header_text_color }};
    border-bottom: 1px solid {{ section.settings.border_color }};
    line-height: 1.4;
  }

  @media screen and (max-width: 767px) {
    .ingredients-table thead th {
      padding: {{ section.settings.header_padding_mobile }}px;
      font-size: {{ section.settings.header_font_size_mobile }}px;
    }
  }

  .ingredients-table tbody tr {
    border-bottom: 1px solid {{ section.settings.border_color }};
    transition: background-color 0.2s ease;
  }

  .ingredients-table tbody tr:hover {
    background: {{ section.settings.row_hover_color }};
  }

  .ingredients-table tbody tr:last-child {
    border-bottom: none;
  }

  .ingredients-table tbody td {
    padding: {{ section.settings.cell_padding }}px;
    text-align: center;
    font-size: {{ section.settings.cell_font_size }}px;
    color: {{ section.settings.cell_text_color }};
    line-height: 1.5;
  }

  @media screen and (max-width: 767px) {
    .ingredients-table tbody td {
      padding: {{ section.settings.cell_padding_mobile }}px;
      font-size: {{ section.settings.cell_font_size_mobile }}px;
    }
  }

  .ingredients-table tbody td:first-child {
    font-weight: 600;
  }

  .ingredients-table-footer {
    margin-top: 0;
    padding: 20px;
    background: #FFFFFF;
    border-radius: 0 0 {{ section.settings.table_border_radius }}px {{ section.settings.table_border_radius }}px;
    font-size: {{ section.settings.footer_font_size }}px;
    color: {{ section.settings.footer_text_color }};
    line-height: 1.6;
    text-align: right;
  }

  @media screen and (max-width: 767px) {
    .ingredients-table-footer {
      font-size: {{ section.settings.footer_font_size_mobile }}px;
      padding: 16px;
    }
  }

  /* Mobile responsive - horizontal scroll */
  @media screen and (max-width: 767px) {
    .ingredients-table-wrapper {
      margin: 0;
      padding: 0;
      overflow-x: visible;
    }

    .ingredients-table {
      min-width: 600px;
      width: 600px;
    }

    .ingredients-table-footer {
      min-width: 600px;
      width: 600px;
    }
  }

  /* Alternative mobile layout - stacked cards */
  @media screen and (max-width: 767px) {
    .ingredients-table.mobile-stacked {
      min-width: auto;
    }

    .ingredients-table.mobile-stacked thead {
      display: none;
    }

    .ingredients-table.mobile-stacked tbody tr {
      display: block;
      margin-bottom: 16px;
      border: 1px solid {{ section.settings.border_color }};
      border-radius: 8px;
      padding: 16px;
    }

    .ingredients-table.mobile-stacked tbody td {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      text-align: right;
      border-bottom: 1px solid {{ section.settings.border_color }};
    }

    .ingredients-table.mobile-stacked tbody td:last-child {
      border-bottom: none;
    }

    .ingredients-table.mobile-stacked tbody td:before {
      content: attr(data-label);
      font-weight: 700;
      margin-left: 16px;
    }
  }
{% endstyle %}

<div class="product-ingredients-table-section" {{ section.shopify_attributes }}>
  <div class="ingredients-table-container">
    {% if section.settings.show_header and section.settings.heading != blank or section.settings.description != blank %}
      <div class="ingredients-table-header">
        {% if section.settings.heading != blank %}
          <h2>{{ section.settings.heading }}</h2>
        {% endif %}
        {% if section.settings.description != blank %}
          <p>{{ section.settings.description }}</p>
        {% endif %}
      </div>
    {% endif %}

    <div class="table-with-footer-wrapper">
      <div class="ingredients-table-wrapper">
        <table class="ingredients-table {% if section.settings.mobile_layout == 'stacked' %}mobile-stacked{% endif %}">
          <thead>
            <tr>
              <th>{{ section.settings.column_1_header }}</th>
              <th>{{ section.settings.column_2_header }}</th>
              <th>{{ section.settings.column_3_header }}</th>
              <th>{{ section.settings.column_4_header }}</th>
            </tr>
          </thead>
          <tbody>
            {% if ingredients_list != blank %}
              {% for ingredient in ingredients_list %}
                {% liquid
                  assign value_1 = ingredient[field_1] | default: '-'
                  assign value_2 = ingredient[field_2] | default: '-'
                  assign value_3 = ingredient[field_3] | default: '-'
                  assign value_4 = ingredient[field_4] | default: '-'
                %}
                <tr>
                  <td data-label="{{ section.settings.column_1_header }}">
                    {{ value_1 }}
                  </td>
                  <td data-label="{{ section.settings.column_2_header }}">
                    {{ value_2 }}
                  </td>
                  <td data-label="{{ section.settings.column_3_header }}">
                    {{ value_3 }}
                  </td>
                  <td data-label="{{ section.settings.column_4_header }}">
                    {{ value_4 }}
                  </td>
                </tr>
              {% endfor %}
            {% else %}
              {% comment %} No data message {% endcomment %}
              <tr>
                <td colspan="4" style="text-align: center; padding: 40px 20px; color: #999;">
                  {% if section.settings.no_data_message != blank %}
                    {{ section.settings.no_data_message }}
                  {% else %}
                    אין מידע תזונתי זמין למוצר זה
                  {% endif %}
                </td>
              </tr>
            {% endif %}
          </tbody>
        </table>
      </div>

      {% if section.settings.footer_text != blank %}
        <div class="ingredients-table-footer">
          {{ section.settings.footer_text }}
        </div>
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Product Ingredients Table",
  "tag": "section",
  "class": "section-product-ingredients-table",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "⚙️ Metafield Configuration"
    },
    {
      "type": "text",
      "id": "metafield_namespace",
      "label": "Metafield Namespace",
      "default": "custom.ingredients_table",
      "info": "The namespace of the product metafield (e.g., custom.ingredients_table)"
    },
    {
      "type": "text",
      "id": "field_1_name",
      "label": "Field 1 Name (Column 1)",
      "default": "vitamin_name",
      "info": "The field name for column 1 (e.g., vitamin_name or vitamin_row.vitamin_name)"
    },
    {
      "type": "text",
      "id": "field_2_name",
      "label": "Field 2 Name (Column 2)",
      "default": "daily_men",
      "info": "The field name for column 2 (e.g., daily_men or vitamin_row.daily_men)"
    },
    {
      "type": "text",
      "id": "field_3_name",
      "label": "Field 3 Name (Column 3)",
      "default": "daily_women",
      "info": "The field name for column 3 (e.g., daily_women or vitamin_row.daily_women)"
    },
    {
      "type": "text",
      "id": "field_4_name",
      "label": "Field 4 Name (Column 4)",
      "default": "amount",
      "info": "The field name for column 4 (e.g., amount or vitamin_row.amount)"
    },
    {
      "type": "header",
      "content": "Section Header"
    },
    {
      "type": "checkbox",
      "id": "show_header",
      "label": "Show section header",
      "default": true
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading size (desktop)",
      "default": 36
    },
    {
      "type": "range",
      "id": "heading_size_mobile",
      "min": 18,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Heading size (mobile)",
      "default": 28
    },
    {
      "type": "range",
      "id": "description_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Description size (desktop)",
      "default": 16
    },
    {
      "type": "range",
      "id": "description_size_mobile",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Description size (mobile)",
      "default": 14
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#666666"
    },
    {
      "type": "header",
      "content": "Table Headers"
    },
    {
      "type": "text",
      "id": "column_1_header",
      "label": "Column 1 Header",
      "default": "ויתמין"
    },
    {
      "type": "text",
      "id": "column_2_header",
      "label": "Column 2 Header",
      "default": "קבוצה יומית ממולצת לגברים"
    },
    {
      "type": "text",
      "id": "column_3_header",
      "label": "Column 3 Header",
      "default": "קבוצה יומית ממולצת לנשים"
    },
    {
      "type": "text",
      "id": "column_4_header",
      "label": "Column 4 Header",
      "default": "כמות ויתמינים למנה"
    },
    {
      "type": "header",
      "content": "Table Styling"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Section background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "table_background",
      "label": "Table background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "header_background",
      "label": "Header background",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "header_text_color",
      "label": "Header text color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "cell_text_color",
      "label": "Cell text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border color",
      "default": "#e0e0e0"
    },
    {
      "type": "color",
      "id": "row_hover_color",
      "label": "Row hover color",
      "default": "#f9f9f9"
    },
    {
      "type": "range",
      "id": "table_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "header_font_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Header font size (desktop)",
      "default": 16
    },
    {
      "type": "range",
      "id": "header_font_size_mobile",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Header font size (mobile)",
      "default": 12
    },
    {
      "type": "range",
      "id": "cell_font_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Cell font size (desktop)",
      "default": 14
    },
    {
      "type": "range",
      "id": "cell_font_size_mobile",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Cell font size (mobile)",
      "default": 12
    },
    {
      "type": "range",
      "id": "header_padding",
      "min": 8,
      "max": 32,
      "step": 2,
      "unit": "px",
      "label": "Header padding (desktop)",
      "default": 20
    },
    {
      "type": "range",
      "id": "header_padding_mobile",
      "min": 6,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Header padding (mobile)",
      "default": 12
    },
    {
      "type": "range",
      "id": "cell_padding",
      "min": 8,
      "max": 32,
      "step": 2,
      "unit": "px",
      "label": "Cell padding (desktop)",
      "default": 16
    },
    {
      "type": "range",
      "id": "cell_padding_mobile",
      "min": 6,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Cell padding (mobile)",
      "default": 10
    },
    {
      "type": "header",
      "content": "Footer"
    },
    {
      "type": "richtext",
      "id": "footer_text",
      "label": "Footer text",
      "default": "<p>* הערות נוספות על המוצר והמרכיבים</p>"
    },
    {
      "type": "color",
      "id": "footer_background",
      "label": "Footer background",
      "default": "#f9f9f9"
    },
    {
      "type": "color",
      "id": "footer_text_color",
      "label": "Footer text color",
      "default": "#666666"
    },
    {
      "type": "range",
      "id": "footer_font_size",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Footer font size (desktop)",
      "default": 14
    },
    {
      "type": "range",
      "id": "footer_font_size_mobile",
      "min": 10,
      "max": 16,
      "step": 1,
      "unit": "px",
      "label": "Footer font size (mobile)",
      "default": 12
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding top (desktop)",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom (desktop)",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Padding top (mobile)",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom (mobile)",
      "default": 40
    },
    {
      "type": "header",
      "content": "Mobile Layout"
    },
    {
      "type": "select",
      "id": "mobile_layout",
      "label": "Mobile layout",
      "options": [
        {
          "value": "scroll",
          "label": "Horizontal scroll"
        },
        {
          "value": "stacked",
          "label": "Stacked cards"
        }
      ],
      "default": "scroll"
    },
    {
      "type": "header",
      "content": "No Data Message"
    },
    {
      "type": "text",
      "id": "no_data_message",
      "label": "Message when no data",
      "default": "אין מידע תזונתי זמין למוצר זה",
      "info": "Displayed when product has no ingredients data"
    }
  ],
  "presets": [
    {
      "name": "Product Ingredients Table"
    }
  ]
}
{% endschema %}

