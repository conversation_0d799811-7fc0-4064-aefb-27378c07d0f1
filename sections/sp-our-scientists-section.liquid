{% schema %}
{
  "name": "SP Our Scientists",
  "settings": [
    { "type": "header", "content": "Header" },
    { "type": "text", "id": "heading", "label": "Heading", "default": "המומחים סומכים עלינו, גם אתם יכולים" },
    { "type": "text", "id": "button_label", "label": "Button Label", "default": "Learn More" },
    { "type": "url", "id": "button_link", "label": "Button Link" },
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image Block",
      "settings": [
        { "type": "image_picker", "id": "thumbnail", "label": "Scientist Thumbnail" },
        { "type": "text", "id": "name", "label": "Name", "default": "Scientist Name" },
        { "type": "textarea", "id": "description", "label": "Description", "default": "Description about the scientist" },
      ]
    },
  ],
  "max_blocks": 50,
  "presets": [
    {
      "name": "SP Our Scientists",
      "category": "Custom",
      "blocks": [
        { "type": "image" },
        { "type": "image" },
        { "type": "image" },
        { "type": "image" }
      ]
    }
  ]
}
{% endschema %}

<style>
.sp__ourScientistsWrapper{
    padding: 0 20px 0 0;
    margin: 0 0 40px;
}
.scientist__itemWrap{}
.scientist__itemWrap .scientist__thumbWrap{
    position: relative;
    width: 100%;
    height: 358px;
    /* aspect-ratio: 140 / 179; */
    overflow: hidden;
    border-radius: 8px;
}
.scientist__itemWrap .scientist__thumbWrap img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}
.scientist__descWrap{
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
    padding: 8px;
}
.scientist__descWrap .desc__wrap{
    border-radius: 8px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.11);
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
    color: #323438;
}
.scientist__descWrap .desc__wrap h3{
    font-size: 16px;
    line-height: 1.3;
    font-weight: 700;
    margin: 0 0 12px;
}
.scientist__descWrap .desc__wrap p{
    font-size: 14px;
    line-height: 1.53;
    font-weight: 500;
    margin: 0;
    height: 65px;
    overflow: hidden;
}

/* Carousel Navigation and Progress Bar Styling */
.glide__carouselNavWrapper{
    display: flex;
    padding: 0;
    margin: 30px 0 0;
}
.glide__carouselNavWrapper .glide__arrows{
    width: 75px;
}
.glide__carouselNavWrapper .glide__arrows .glide__arrow{
    background: none;
    border: none;
}

.glide__carouselNavWrapper .glide__arrow.glide__arrow--right {
    scale: -1 1;
}
.glide__carouselNavWrapper .glide__progressBarWrap{
    display: flex;
    align-items: center;
    width: calc(100% - 75px);
    padding-left: 15px;
}
.glide__carouselNavWrapper .glide__progressWrapper {
    /* flex: 1; */
    height: 2px;
    background-color: #D0D1D5;
    border-radius: 2px;
    overflow: hidden;
    width: 100%;
}
.glide__carouselNavWrapper .glide__progressBar {
    height: 100%;
    width: 0%;
    background-color: #616266;
    transition: width 0.3s ease-out;
}

</style>

<div class="sp__ourScientistsWrapper">
    <div class="glide" id="scientistsCarousel">
      <div class="glide__track" data-glide-el="track">
        <ul class="glide__slides">
          {% for block in section.blocks %}
            {% if block.settings.thumbnail != blank %}
              <li class="glide__slide">
                <div class="scientist__itemWrap">
                    <div class="scientist__thumbWrap">
                        <img src="{{ block.settings.thumbnail | image_url: width: 1200 }}" alt="{{ block.settings.name }}" />
                        <div class="scientist__descWrap">
                            <div class="desc__wrap">
                                <h3>{{ block.settings.name }}</h3>
                                <p>{{ block.settings.description }}</p>
                            </div>
                        </div>
                    </div>
                </div>
              </li>
            {% endif %}
          {% endfor %}
        </ul>
      </div>
      <div class="glide__carouselNavWrapper">
        <div class="glide__progressBarWrap">
            <div class="glide__progressWrapper" dir="rtl">
                <div class="glide__progressBar" dir="rtl"></div>
            </div>
        </div>
        <div class="glide__arrows" data-glide-el="controls">
            <button class="glide__arrow glide__arrow--right" data-glide-dir=">">
                <svg width="18" height="8" viewBox="0 0 18 8" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.625 7.125L1.5 4M1.5 4L4.625 0.875M1.5 4H16.5" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
            <button class="glide__arrow glide__arrow--left" data-glide-dir="<">
                <svg width="18" height="8" viewBox="0 0 18 8" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.625 7.125L1.5 4M1.5 4L4.625 0.875M1.5 4H16.5" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>
      </div>
    </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    //const rtl = {{ section.settings.rtl_enable | json }};
    //const slideCount = {{ section.settings.slide_per_view | default: 1 }};
    //const slideOffset = {{ section.settings.slide_visibility | default: 0 }};
    //const perView = slideCount + (slideOffset / 100);

    var glideCarousel = new Glide('#scientistsCarousel', {
      type: 'slider',
      rewind: false,
      startAt: 0,
      perView: 1,
      touchRatio: 1,
      gap: 20,
      //animationTimingFunc: 'ease-in-out',
      animationDuration: 400,
      direction: 'rtl',
      peek: {
        before: 0,
        after: 100
      },
      //breakpoints: {
      //  768: {
      //    perView: 1
      //  }
      //}
    });

    //glideCarousel.mount();

    const progressBar = document.querySelector(".glide__progressBar");

    glideCarousel.on(["mount.after", "run"], function () {
      const totalSlides = glideCarousel._c.Html.slides.length;
      const currentIndex = glideCarousel.index;

      // Calculate percentage
      let progress = ((currentIndex + 1) / totalSlides) * 100;

      // Since direction is RTL, we update width from the right
      progressBar.style.width = progress + "%";
    });

    glideCarousel.mount();

  });
</script>
