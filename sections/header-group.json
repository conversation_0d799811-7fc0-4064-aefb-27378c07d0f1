/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "type": "header",
  "name": "t:names.header",
  "sections": {
    "header_announcements_ANmzVn": {
      "type": "header-announcements",
      "blocks": {
        "announcement_hzPXaX": {
          "type": "_announcement",
          "settings": {
            "text": "משלוח חינם בקנייה מעל 350₪",
            "link": "",
            "font": "var(--font-body--family)",
            "font_size": "0.875rem",
            "weight": "",
            "letter_spacing": "normal",
            "case": "none"
          },
          "blocks": {}
        },
        "announcement_UiCtG3": {
          "type": "_announcement",
          "name": "t:names.announcement",
          "settings": {
            "text": "משלוח מהיר 3-5 ימי עסקים",
            "link": "",
            "font": "var(--font-body--family)",
            "font_size": "0.875rem",
            "weight": "",
            "letter_spacing": "normal",
            "case": "none"
          },
          "blocks": {}
        }
      },
      "block_order": [
        "announcement_hzPXaX",
        "announcement_UiCtG3"
      ],
      "disabled": true,
      "name": "t:names.announcement_bar",
      "settings": {
        "speed": 5,
        "section_width": "full-width",
        "color_scheme": "scheme-6",
        "divider_width": 0,
        "padding-block-start": 12,
        "padding-block-end": 20
      }
    },
    "header_section": {
      "type": "header",
      "blocks": {
        "header-logo": {
          "type": "_header-logo",
          "static": true,
          "settings": {
            "hide_logo_on_home_page": false,
            "custom_height": 22,
            "custom_height_mobile": 22,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "header-menu": {
          "type": "_header-menu",
          "static": true,
          "settings": {
            "menu": "main-menu",
            "menu_style": "featured_products",
            "featured_products_aspect_ratio": "adapt",
            "featured_collections_aspect_ratio": "16 / 9",
            "image_border_radius": 0,
            "product_title_case": "uppercase",
            "collection_title_case": "default",
            "color_scheme": "",
            "menu_font_style": "inverse",
            "type_font_primary_link": "subheading",
            "type_font_primary_size": "var(--font-size--body-md)",
            "type_case_primary_link": "none",
            "type_font_secondary_link": "secondary",
            "type_case_secondary_link": "none",
            "type_font_tertiary_link": "secondary",
            "type_case_tertiary_link": "none",
            "navigation_bar": false,
            "color_scheme_navigation_bar": "",
            "drawer_accordion": false,
            "drawer_accordion_expand_first": false,
            "drawer_dividers": false
          },
          "blocks": {}
        }
      },
      "custom_css": [
        "{direction: rtl;}",
        ".button {padding: inherit;}",
        ".header__column--right a.size-style.button-secondary {display: none;}"
      ],
      "settings": {
        "logo_position": "center",
        "menu_position": "left",
        "menu_row": "top",
        "show_search": true,
        "search_position": "right",
        "search_row": "top",
        "show_country": true,
        "country_selector_style": false,
        "show_language": true,
        "localization_position": "left",
        "localization_row": "top",
        "section_width": "page-width",
        "section_height": "standard",
        "enable_sticky_header": "always",
        "divider_width": 0,
        "divider_size": "page-width",
        "border_width": 0,
        "color_scheme_top": "scheme-58084d4c-a86e-4d0a-855e-a0966e5043f7",
        "color_scheme_bottom": "",
        "color_scheme_transparent": "scheme-2",
        "enable_transparent_header_home": true,
        "home_color_scheme": "inverse",
        "enable_transparent_header_product": false,
        "product_color_scheme": "default",
        "enable_transparent_header_collection": false,
        "collection_color_scheme": "default"
      }
    }
  },
  "order": [
    "header_announcements_ANmzVn",
    "header_section"
  ]
}
