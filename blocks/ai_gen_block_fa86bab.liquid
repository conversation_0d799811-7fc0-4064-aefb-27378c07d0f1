{% doc %}
  @prompt
    Refine the existing "How to Use" section to match a minimalist, elegant design style similar to the “collection card” layout.
    
    Design details:
    - White or very light background
    - Center-aligned content
    - Step number in a thin outlined circle (not filled)
    - Thin horizontal progress line between steps (like a timeline), very subtle and elegant
    - Rounded image corners (8–12px radius)
    - Clean typography (use same font size and weight as product card titles)
    - Minimal spacing, consistent vertical rhythm
    - Remove large arrow buttons — replace with a thin progress bar that fills as the user swipes through steps
    - The section should feel light, balanced, and visually aligned with product cards on the site
    
    Structure:
    - Heading: “איך להשתמש” (centered)
    - Each step includes:
      • Step number (outlined circle)
      • Image (uploadable)
      • Short title (2–3 words)
      • Subtext (1 short line)
      
    Ensure full RTL alignment and Hebrew text support (fonts like Assistant or Heeb<PERSON>).
    
{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-how-to-use-{{ ai_gen_id }} {
    background-color: {{ block.settings.background_color }};
    padding: {{ block.settings.section_padding }}px 20px;
    direction: rtl;
    text-align: center;
  }

  .ai-how-to-use-container-{{ ai_gen_id }} {
    max-width: 1200px;
    margin: 0 auto;
  }

  .ai-how-to-use-heading-{{ ai_gen_id }} {
    font-size: {{ block.settings.heading_size }}px;
    margin: 0 0 {{ block.settings.spacing_between_heading_and_steps }}px;
    color: {{ block.settings.heading_color }};
    font-weight: 300;
  }

  .ai-how-to-use-steps-wrapper-{{ ai_gen_id }} {
    position: relative;
    overflow-x: auto;
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .ai-how-to-use-steps-wrapper-{{ ai_gen_id }}::-webkit-scrollbar {
    display: none;
  }

  .ai-how-to-use-steps-{{ ai_gen_id }} {
    display: flex;
    gap: {{ block.settings.gap_between_steps }}px;
    padding: 40px 0;
    position: relative;
  }

  .ai-how-to-use-timeline-{{ ai_gen_id }} {
    position: absolute;
    top: {{ block.settings.step_number_size | divided_by: 2 }}px;
    right: 0;
    left: 0;
    height: 1px;
    background-color: {{ block.settings.timeline_color }};
    opacity: {{ block.settings.timeline_opacity | divided_by: 100.0 }};
    z-index: 0;
  }

  .ai-how-to-use-step-{{ ai_gen_id }} {
    flex: 0 0 {{ block.settings.step_width }}px;
    min-width: {{ block.settings.step_width }}px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: {{ block.settings.gap_within_step }}px;
  }

  .ai-how-to-use-step-number-{{ ai_gen_id }} {
    width: {{ block.settings.step_number_size }}px;
    height: {{ block.settings.step_number_size }}px;
    border: {{ block.settings.step_number_border_width }}px solid {{ block.settings.step_number_color }};
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: {{ block.settings.step_number_font_size }}px;
    color: {{ block.settings.step_number_color }};
    background-color: {{ block.settings.background_color }};
    position: relative;
    z-index: 1;
    font-weight: 400;
  }

  .ai-how-to-use-step-image-{{ ai_gen_id }} {
    width: 100%;
    border-radius: {{ block.settings.image_border_radius }}px;
    overflow: hidden;
  }

  .ai-how-to-use-step-image-{{ ai_gen_id }} img {
    width: 100%;
    height: auto;
    display: block;
  }

  .ai-how-to-use-step-image-placeholder-{{ ai_gen_id }} {
    width: 100%;
    aspect-ratio: 1;
    background-color: #f4f4f4;
    border-radius: {{ block.settings.image_border_radius }}px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-how-to-use-step-image-placeholder-{{ ai_gen_id }} svg {
    width: 60%;
    height: 60%;
    opacity: 0.3;
  }

  .ai-how-to-use-step-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.step_title_size }}px;
    font-weight: {{ block.settings.step_title_weight }};
    color: {{ block.settings.step_title_color }};
    margin: 0;
    line-height: 1.3;
  }

  .ai-how-to-use-step-subtext-{{ ai_gen_id }} {
    font-size: {{ block.settings.step_subtext_size }}px;
    color: {{ block.settings.step_subtext_color }};
    margin: 0;
    line-height: 1.5;
    opacity: 0.8;
  }

  .ai-how-to-use-progress-bar-{{ ai_gen_id }} {
    width: 100%;
    height: 2px;
    background-color: {{ block.settings.progress_bar_bg_color }};
    margin-top: 30px;
    border-radius: 1px;
    overflow: hidden;
  }

  .ai-how-to-use-progress-fill-{{ ai_gen_id }} {
    height: 100%;
    background-color: {{ block.settings.progress_bar_fill_color }};
    width: 0%;
    transition: width 0.3s ease;
  }

  @media screen and (max-width: 749px) {
    .ai-how-to-use-{{ ai_gen_id }} {
      padding: {{ block.settings.section_padding | times: 0.7 }}px 16px;
    }

    .ai-how-to-use-heading-{{ ai_gen_id }} {
      font-size: {{ block.settings.heading_size | times: 0.7 }}px;
      margin-bottom: {{ block.settings.spacing_between_heading_and_steps | times: 0.7 }}px;
    }

    .ai-how-to-use-step-{{ ai_gen_id }} {
      flex: 0 0 {{ block.settings.step_width | times: 0.85 }}px;
      min-width: {{ block.settings.step_width | times: 0.85 }}px;
    }

    .ai-how-to-use-steps-{{ ai_gen_id }} {
      gap: {{ block.settings.gap_between_steps | times: 0.7 }}px;
    }
  }
{% endstyle %}

<how-to-use-{{ ai_gen_id }} class="ai-how-to-use-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-how-to-use-container-{{ ai_gen_id }}">
    {% if block.settings.heading != blank %}
      <h2 class="ai-how-to-use-heading-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
    {% endif %}

    <div class="ai-how-to-use-steps-wrapper-{{ ai_gen_id }}" data-steps-wrapper>
      <div class="ai-how-to-use-steps-{{ ai_gen_id }}">
        <div class="ai-how-to-use-timeline-{{ ai_gen_id }}"></div>

        {% for i in (1..5) %}
          {% liquid
            assign step_enabled_key = 'step_' | append: i | append: '_enabled'
            assign step_title_key = 'step_' | append: i | append: '_title'
            assign step_subtext_key = 'step_' | append: i | append: '_subtext'
            assign step_image_key = 'step_' | append: i | append: '_image'

            assign step_enabled = block.settings[step_enabled_key]
            assign step_title = block.settings[step_title_key]
            assign step_subtext = block.settings[step_subtext_key]
            assign step_image = block.settings[step_image_key]
          %}

          {% if step_enabled %}
            <div class="ai-how-to-use-step-{{ ai_gen_id }}" data-step="{{ i }}">
              <div class="ai-how-to-use-step-number-{{ ai_gen_id }}">{{ i }}</div>

              <div class="ai-how-to-use-step-image-{{ ai_gen_id }}">
                {% if step_image %}
                  <img
                    src="{{ step_image | image_url: width: 600 }}"
                    alt="{{ step_title | escape }}"
                    loading="lazy"
                    width="{{ step_image.width }}"
                    height="{{ step_image.height }}"
                  >
                {% else %}
                  <div class="ai-how-to-use-step-image-placeholder-{{ ai_gen_id }}">
                    {{ 'product-1' | placeholder_svg_tag }}
                  </div>
                {% endif %}
              </div>

              {% if step_title != blank %}
                <h3 class="ai-how-to-use-step-title-{{ ai_gen_id }}">{{ step_title }}</h3>
              {% endif %}

              {% if step_subtext != blank %}
                <p class="ai-how-to-use-step-subtext-{{ ai_gen_id }}">{{ step_subtext }}</p>
              {% endif %}
            </div>
          {% endif %}
        {% endfor %}
      </div>
    </div>

    <div class="ai-how-to-use-progress-bar-{{ ai_gen_id }}">
      <div class="ai-how-to-use-progress-fill-{{ ai_gen_id }}" data-progress-fill></div>
    </div>
  </div>
</how-to-use-{{ ai_gen_id }}>

<script>
  (function() {
    class HowToUse{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
      }

      connectedCallback() {
        this.stepsWrapper = this.querySelector('[data-steps-wrapper]');
        this.progressFill = this.querySelector('[data-progress-fill]');
        this.steps = this.querySelectorAll('[data-step]');

        if (this.stepsWrapper && this.progressFill && this.steps.length > 0) {
          this.updateProgress();
          this.stepsWrapper.addEventListener('scroll', this.updateProgress.bind(this));
        }
      }

      updateProgress() {
        const scrollLeft = this.stepsWrapper.scrollLeft;
        const scrollWidth = this.stepsWrapper.scrollWidth - this.stepsWrapper.clientWidth;
        const scrollPercentage = scrollWidth > 0 ? (scrollLeft / scrollWidth) * 100 : 0;
        this.progressFill.style.width = scrollPercentage + '%';
      }
    }

    customElements.define('how-to-use-{{ ai_gen_id }}', HowToUse{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "איך להשתמש",
  "settings": [
    {
      "type": "header",
      "content": "כותרת"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "כותרת",
      "default": "איך להשתמש"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "גודל כותרת",
      "default": 32
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "צבע כותרת",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "spacing_between_heading_and_steps",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "מרווח מתחת לכותרת",
      "default": 40
    },
    {
      "type": "header",
      "content": "עיצוב כללי"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "צבע רקע",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "section_padding",
      "min": 20,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "ריפוד מקטע",
      "default": 60
    },
    {
      "type": "range",
      "id": "step_width",
      "min": 150,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "רוחב שלב",
      "default": 220
    },
    {
      "type": "range",
      "id": "gap_between_steps",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "מרווח בין שלבים",
      "default": 40
    },
    {
      "type": "range",
      "id": "gap_within_step",
      "min": 8,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "מרווח בתוך שלב",
      "default": 16
    },
    {
      "type": "header",
      "content": "מספר שלב"
    },
    {
      "type": "range",
      "id": "step_number_size",
      "min": 30,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "גודל עיגול",
      "default": 40
    },
    {
      "type": "range",
      "id": "step_number_font_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "גודל מספר",
      "default": 16
    },
    {
      "type": "range",
      "id": "step_number_border_width",
      "min": 1,
      "max": 3,
      "step": 1,
      "unit": "px",
      "label": "עובי מסגרת",
      "default": 1
    },
    {
      "type": "color",
      "id": "step_number_color",
      "label": "צבע מספר ומסגרת",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "קו זמן"
    },
    {
      "type": "color",
      "id": "timeline_color",
      "label": "צבע קו",
      "default": "#e6e6e6"
    },
    {
      "type": "range",
      "id": "timeline_opacity",
      "min": 10,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "שקיפות קו",
      "default": 30
    },
    {
      "type": "header",
      "content": "תמונה"
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "עיגול פינות",
      "default": 10
    },
    {
      "type": "header",
      "content": "כותרת שלב"
    },
    {
      "type": "range",
      "id": "step_title_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "גודל טקסט",
      "default": 16
    },
    {
      "type": "range",
      "id": "step_title_weight",
      "min": 300,
      "max": 700,
      "step": 100,
      "label": "משקל גופן",
      "default": 500
    },
    {
      "type": "color",
      "id": "step_title_color",
      "label": "צבע",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "תת-טקסט שלב"
    },
    {
      "type": "range",
      "id": "step_subtext_size",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "גודל טקסט",
      "default": 13
    },
    {
      "type": "color",
      "id": "step_subtext_color",
      "label": "צבע",
      "default": "#666666"
    },
    {
      "type": "header",
      "content": "סרגל התקדמות"
    },
    {
      "type": "color",
      "id": "progress_bar_bg_color",
      "label": "צבע רקע",
      "default": "#e6e6e6"
    },
    {
      "type": "color",
      "id": "progress_bar_fill_color",
      "label": "צבע מילוי",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "שלב 1"
    },
    {
      "type": "checkbox",
      "id": "step_1_enabled",
      "label": "הפעל שלב 1",
      "default": true
    },
    {
      "type": "image_picker",
      "id": "step_1_image",
      "label": "תמונה"
    },
    {
      "type": "text",
      "id": "step_1_title",
      "label": "כותרת",
      "default": "הכנה"
    },
    {
      "type": "text",
      "id": "step_1_subtext",
      "label": "תת-טקסט",
      "default": "נקו את האזור"
    },
    {
      "type": "header",
      "content": "שלב 2"
    },
    {
      "type": "checkbox",
      "id": "step_2_enabled",
      "label": "הפעל שלב 2",
      "default": true
    },
    {
      "type": "image_picker",
      "id": "step_2_image",
      "label": "תמונה"
    },
    {
      "type": "text",
      "id": "step_2_title",
      "label": "כותרת",
      "default": "יישום"
    },
    {
      "type": "text",
      "id": "step_2_subtext",
      "label": "תת-טקסט",
      "default": "מרחו בעדינות"
    },
    {
      "type": "header",
      "content": "שלב 3"
    },
    {
      "type": "checkbox",
      "id": "step_3_enabled",
      "label": "הפעל שלב 3",
      "default": true
    },
    {
      "type": "image_picker",
      "id": "step_3_image",
      "label": "תמונה"
    },
    {
      "type": "text",
      "id": "step_3_title",
      "label": "כותרת",
      "default": "המתנה"
    },
    {
      "type": "text",
      "id": "step_3_subtext",
      "label": "תת-טקסט",
      "default": "המתינו 5 דקות"
    },
    {
      "type": "header",
      "content": "שלב 4"
    },
    {
      "type": "checkbox",
      "id": "step_4_enabled",
      "label": "הפעל שלב 4",
      "default": false
    },
    {
      "type": "image_picker",
      "id": "step_4_image",
      "label": "תמונה"
    },
    {
      "type": "text",
      "id": "step_4_title",
      "label": "כותרת"
    },
    {
      "type": "text",
      "id": "step_4_subtext",
      "label": "תת-טקסט"
    },
    {
      "type": "header",
      "content": "שלב 5"
    },
    {
      "type": "checkbox",
      "id": "step_5_enabled",
      "label": "הפעל שלב 5",
      "default": false
    },
    {
      "type": "image_picker",
      "id": "step_5_image",
      "label": "תמונה"
    },
    {
      "type": "text",
      "id": "step_5_title",
      "label": "כותרת"
    },
    {
      "type": "text",
      "id": "step_5_subtext",
      "label": "תת-טקסט"
    }
  ],
  "presets": [
    {
      "name": "איך להשתמש"
    }
  ]
}
{% endschema %}
