{% doc %}
  @prompt
    make a dynamic scrolling text with icon strip for 6-8 benefits. for each make text + icon (RTL)
    Liposomal absorption ×4–5 — Icon: Molecule
    
    Research-based formula — Icon: Lab flask
    
    Visible results — Icon: Eye
    
    No sugar or preservatives — Icon: Leaf
    
    Doctor-recommended — Icon: Stethoscope
    
    Made in Israel — Icon: Flag
    
    Daily use routine — Icon: Calendar
    
    Liquid & tasty — Icon: Spoon with drop
{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-scrolling-strip-{{ ai_gen_id }} {
    width: 100%;
    overflow: hidden;
    background-color: {{ block.settings.background_color }};
    padding: {{ block.settings.padding_vertical }}px 0;
    position: relative;
  }

  .ai-scrolling-strip-track-{{ ai_gen_id }} {
    display: flex;
    width: max-content;
    gap: {{ block.settings.item_spacing }}px;
    animation: ai-scroll-{{ ai_gen_id }} {{ block.settings.scroll_speed }}s linear infinite;
    direction: rtl;
  }

  .ai-scrolling-strip-{{ ai_gen_id }}:hover .ai-scrolling-strip-track-{{ ai_gen_id }} {
    animation-play-state: paused;
  }

  @keyframes ai-scroll-{{ ai_gen_id }} {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }

  .ai-scrolling-strip-item-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: {{ block.settings.icon_text_gap }}px;
    white-space: nowrap;
    flex-shrink: 0;
    direction: rtl;
  }

  .ai-scrolling-strip-icon-{{ ai_gen_id }} {
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-scrolling-strip-icon-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    stroke: {{ block.settings.icon_color }};
    fill: none;
    stroke-width: {{ block.settings.icon_stroke_width }};
    stroke-linecap: round;
    stroke-linejoin: round;
  }

  .ai-scrolling-strip-text-{{ ai_gen_id }} {
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.text_size }}px;
    font-weight: {{ block.settings.text_weight }};
    direction: rtl;
  }

  .ai-scrolling-strip-separator-{{ ai_gen_id }} {
    color: {{ block.settings.separator_color }};
    font-size: {{ block.settings.text_size }}px;
    margin: 0 {{ block.settings.separator_spacing }}px;
  }

  @media screen and (max-width: 749px) {
    .ai-scrolling-strip-icon-{{ ai_gen_id }} {
      width: {{ block.settings.icon_size | times: 0.8 }}px;
      height: {{ block.settings.icon_size | times: 0.8 }}px;
    }

    .ai-scrolling-strip-text-{{ ai_gen_id }} {
      font-size: {{ block.settings.text_size | times: 0.9 }}px;
    }
  }
{% endstyle %}

<div class="ai-scrolling-strip-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-scrolling-strip-track-{{ ai_gen_id }}">
    {% liquid
      assign benefits = ''
      assign benefits = benefits | append: block.settings.benefit_1_text | append: '||' | append: 'molecule'
      assign benefits = benefits | append: '::' | append: block.settings.benefit_2_text | append: '||' | append: 'flask'
      assign benefits = benefits | append: '::' | append: block.settings.benefit_3_text | append: '||' | append: 'eye'
      assign benefits = benefits | append: '::' | append: block.settings.benefit_4_text | append: '||' | append: 'leaf'
      assign benefits = benefits | append: '::' | append: block.settings.benefit_5_text | append: '||' | append: 'stethoscope'
      assign benefits = benefits | append: '::' | append: block.settings.benefit_6_text | append: '||' | append: 'flag'
      assign benefits = benefits | append: '::' | append: block.settings.benefit_7_text | append: '||' | append: 'calendar'
      assign benefits = benefits | append: '::' | append: block.settings.benefit_8_text | append: '||' | append: 'spoon'
      assign benefits_array = benefits | split: '::'
    %}

    {% for i in (1..2) %}
      {% for benefit_data in benefits_array %}
        {% assign benefit_parts = benefit_data | split: '||' %}
        {% assign benefit_text = benefit_parts[0] %}
        {% assign benefit_icon = benefit_parts[1] %}

        {% if benefit_text != blank %}
          <div class="ai-scrolling-strip-item-{{ ai_gen_id }}">
            <div class="ai-scrolling-strip-icon-{{ ai_gen_id }}">
              {% if benefit_icon == 'molecule' %}
                <svg viewBox="0 0 24 24">
                  <circle cx="6" cy="6" r="3"/>
                  <circle cx="18" cy="6" r="3"/>
                  <circle cx="12" cy="18" r="3"/>
                  <line x1="8" y1="7" x2="10" y2="16"/>
                  <line x1="16" y1="7" x2="14" y2="16"/>
                </svg>
              {% elsif benefit_icon == 'flask' %}
                <svg viewBox="0 0 24 24">
                  <path d="M9 3h6v7l5 8a2 2 0 0 1-2 3H6a2 2 0 0 1-2-3l5-8V3z"/>
                  <line x1="9" y1="3" x2="15" y2="3"/>
                </svg>
              {% elsif benefit_icon == 'eye' %}
                <svg viewBox="0 0 24 24">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                  <circle cx="12" cy="12" r="3"/>
                </svg>
              {% elsif benefit_icon == 'leaf' %}
                <svg viewBox="0 0 24 24">
                  <path d="M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z"/>
                  <path d="M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12"/>
                </svg>
              {% elsif benefit_icon == 'stethoscope' %}
                <svg viewBox="0 0 24 24">
                  <path d="M4.8 2.3A.3.3 0 1 0 5 2H4a2 2 0 0 0-2 2v5a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6V4a2 2 0 0 0-2-2h-1a.2.2 0 1 0 .3.3"/>
                  <path d="M8 15v1a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6v-4"/>
                  <circle cx="20" cy="10" r="2"/>
                </svg>
              {% elsif benefit_icon == 'flag' %}
                <svg viewBox="0 0 24 24">
                  <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/>
                  <line x1="4" y1="22" x2="4" y2="15"/>
                </svg>
              {% elsif benefit_icon == 'calendar' %}
                <svg viewBox="0 0 24 24">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                  <line x1="16" y1="2" x2="16" y2="6"/>
                  <line x1="8" y1="2" x2="8" y2="6"/>
                  <line x1="3" y1="10" x2="21" y2="10"/>
                </svg>
              {% elsif benefit_icon == 'spoon' %}
                <svg viewBox="0 0 24 24">
                  <path d="M7 13c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4h1V2h2v7h1c2.21 0 4 1.79 4 4z"/>
                  <path d="M12 11v10"/>
                  <circle cx="12" cy="18" r="2"/>
                </svg>
              {% endif %}
            </div>
            <span class="ai-scrolling-strip-text-{{ ai_gen_id }}">{{ benefit_text }}</span>
          </div>

          {% unless forloop.last and i == 2 %}
            <span class="ai-scrolling-strip-separator-{{ ai_gen_id }}">{{ block.settings.separator_symbol }}</span>
          {% endunless %}
        {% endif %}
      {% endfor %}
    {% endfor %}
  </div>
</div>

{% schema %}
{
  "name": "Scrolling benefits strip",
  "settings": [
    {
      "type": "header",
      "content": "Benefits"
    },
    {
      "type": "text",
      "id": "benefit_1_text",
      "label": "Benefit 1",
      "default": "ספיגה ליפוזומלית ×4–5"
    },
    {
      "type": "text",
      "id": "benefit_2_text",
      "label": "Benefit 2",
      "default": "נוסחה מבוססת מחקר"
    },
    {
      "type": "text",
      "id": "benefit_3_text",
      "label": "Benefit 3",
      "default": "תוצאות נראות לעין"
    },
    {
      "type": "text",
      "id": "benefit_4_text",
      "label": "Benefit 4",
      "default": "ללא סוכר או חומרים משמרים"
    },
    {
      "type": "text",
      "id": "benefit_5_text",
      "label": "Benefit 5",
      "default": "מומלץ על ידי רופאים"
    },
    {
      "type": "text",
      "id": "benefit_6_text",
      "label": "Benefit 6",
      "default": "מיוצר בישראל"
    },
    {
      "type": "text",
      "id": "benefit_7_text",
      "label": "Benefit 7",
      "default": "שגרת שימוש יומית"
    },
    {
      "type": "text",
      "id": "benefit_8_text",
      "label": "Benefit 8",
      "default": "נוזלי וטעים"
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background",
      "default": "#f9f8f6"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icon",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 14
    },
    {
      "type": "range",
      "id": "text_weight",
      "min": 300,
      "max": 700,
      "step": 100,
      "label": "Text weight",
      "default": 400
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 16,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Icon size",
      "default": 20
    },
    {
      "type": "range",
      "id": "icon_stroke_width",
      "min": 1,
      "max": 3,
      "step": 0.5,
      "label": "Icon stroke width",
      "default": 2
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "padding_vertical",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Vertical padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "icon_text_gap",
      "min": 4,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Icon to text gap",
      "default": 8
    },
    {
      "type": "range",
      "id": "item_spacing",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Item spacing",
      "default": 40
    },
    {
      "type": "header",
      "content": "Separator"
    },
    {
      "type": "text",
      "id": "separator_symbol",
      "label": "Symbol",
      "default": "•"
    },
    {
      "type": "color",
      "id": "separator_color",
      "label": "Color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "separator_spacing",
      "min": 10,
      "max": 40,
      "step": 5,
      "unit": "px",
      "label": "Spacing",
      "default": 20
    },
    {
      "type": "header",
      "content": "Animation"
    },
    {
      "type": "range",
      "id": "scroll_speed",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "s",
      "label": "Scroll speed",
      "default": 30
    }
  ],
  "presets": [
    {
      "name": "Scrolling benefits strip"
    }
  ]
}
{% endschema %}
