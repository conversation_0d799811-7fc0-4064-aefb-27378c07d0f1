{% doc %}
  @prompt
    create a dynamic scrolling text section with 3-4 benefits.
{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-scrolling-benefits-{{ ai_gen_id }} {
    width: 100%;
    overflow: hidden;
    background-color: {{ block.settings.background_color }};
    padding: {{ block.settings.padding_vertical }}px 0;
  }

  .ai-scrolling-benefits-track-{{ ai_gen_id }} {
    display: flex;
    gap: {{ block.settings.gap }}px;
    animation: ai-scroll-{{ ai_gen_id }} {{ block.settings.scroll_speed }}s linear infinite;
    will-change: transform;
  }

  .ai-scrolling-benefits-{{ ai_gen_id }}:hover .ai-scrolling-benefits-track-{{ ai_gen_id }} {
    animation-play-state: paused;
  }

  .ai-scrolling-benefits-item-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: {{ block.settings.icon_text_gap }}px;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .ai-scrolling-benefits-icon-{{ ai_gen_id }} {
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    flex-shrink: 0;
  }

  .ai-scrolling-benefits-icon-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    fill: none;
    stroke: {{ block.settings.icon_color }};
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
  }

  .ai-scrolling-benefits-text-{{ ai_gen_id }} {
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.text_size }}px;
    font-weight: {{ block.settings.text_weight }};
  }

  @keyframes ai-scroll-{{ ai_gen_id }} {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }

  @media screen and (max-width: 749px) {
    .ai-scrolling-benefits-text-{{ ai_gen_id }} {
      font-size: {{ block.settings.text_size | times: 0.85 }}px;
    }

    .ai-scrolling-benefits-icon-{{ ai_gen_id }} {
      width: {{ block.settings.icon_size | times: 0.85 }}px;
      height: {{ block.settings.icon_size | times: 0.85 }}px;
    }
  }
{% endstyle %}

<scrolling-benefits-{{ ai_gen_id }}
  class="ai-scrolling-benefits-{{ ai_gen_id }}"
  {{ block.shopify_attributes }}
>
  <div class="ai-scrolling-benefits-track-{{ ai_gen_id }}">
    {% liquid
      assign benefits = ''
      assign benefit_1 = block.settings.benefit_1
      assign benefit_2 = block.settings.benefit_2
      assign benefit_3 = block.settings.benefit_3
      assign benefit_4 = block.settings.benefit_4

      if benefit_1 != blank
        assign benefits = benefits | append: benefit_1 | append: '||'
      endif
      if benefit_2 != blank
        assign benefits = benefits | append: benefit_2 | append: '||'
      endif
      if benefit_3 != blank
        assign benefits = benefits | append: benefit_3 | append: '||'
      endif
      if benefit_4 != blank
        assign benefits = benefits | append: benefit_4 | append: '||'
      endif

      assign benefits_array = benefits | split: '||'
      assign benefits_count = benefits_array.size
    %}

    {% if benefits_count > 0 %}
      {% for i in (1..2) %}
        {% for benefit in benefits_array %}
          {% if benefit != blank %}
            <div class="ai-scrolling-benefits-item-{{ ai_gen_id }}">
              <div class="ai-scrolling-benefits-icon-{{ ai_gen_id }}">
                {% if block.settings.icon_style == 'checkmark' %}
                  <svg viewBox="0 0 24 24">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                {% elsif block.settings.icon_style == 'star' %}
                  <svg viewBox="0 0 24 24">
                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                  </svg>
                {% elsif block.settings.icon_style == 'heart' %}
                  <svg viewBox="0 0 24 24">
                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                  </svg>
                {% elsif block.settings.icon_style == 'truck' %}
                  <svg viewBox="0 0 24 24">
                    <rect x="1" y="3" width="15" height="13"></rect>
                    <polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon>
                    <circle cx="5.5" cy="18.5" r="2.5"></circle>
                    <circle cx="18.5" cy="18.5" r="2.5"></circle>
                  </svg>
                {% elsif block.settings.icon_style == 'shield' %}
                  <svg viewBox="0 0 24 24">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                  </svg>
                {% elsif block.settings.icon_style == 'gift' %}
                  <svg viewBox="0 0 24 24">
                    <polyline points="20 12 20 22 4 22 4 12"></polyline>
                    <rect x="2" y="7" width="20" height="5"></rect>
                    <line x1="12" y1="22" x2="12" y2="7"></line>
                    <path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"></path>
                    <path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"></path>
                  </svg>
                {% endif %}
              </div>
              <span class="ai-scrolling-benefits-text-{{ ai_gen_id }}">{{ benefit }}</span>
            </div>
          {% endif %}
        {% endfor %}
      {% endfor %}
    {% else %}
      <div class="ai-scrolling-benefits-item-{{ ai_gen_id }}">
        <div class="ai-scrolling-benefits-icon-{{ ai_gen_id }}">
          <svg viewBox="0 0 24 24">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
        <span class="ai-scrolling-benefits-text-{{ ai_gen_id }}">Free shipping on orders over $50</span>
      </div>
      <div class="ai-scrolling-benefits-item-{{ ai_gen_id }}">
        <div class="ai-scrolling-benefits-icon-{{ ai_gen_id }}">
          <svg viewBox="0 0 24 24">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
        <span class="ai-scrolling-benefits-text-{{ ai_gen_id }}">30-day money back guarantee</span>
      </div>
      <div class="ai-scrolling-benefits-item-{{ ai_gen_id }}">
        <div class="ai-scrolling-benefits-icon-{{ ai_gen_id }}">
          <svg viewBox="0 0 24 24">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
        <span class="ai-scrolling-benefits-text-{{ ai_gen_id }}">Secure checkout</span>
      </div>
      <div class="ai-scrolling-benefits-item-{{ ai_gen_id }}">
        <div class="ai-scrolling-benefits-icon-{{ ai_gen_id }}">
          <svg viewBox="0 0 24 24">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
        <span class="ai-scrolling-benefits-text-{{ ai_gen_id }}">24/7 customer support</span>
      </div>
      <div class="ai-scrolling-benefits-item-{{ ai_gen_id }}">
        <div class="ai-scrolling-benefits-icon-{{ ai_gen_id }}">
          <svg viewBox="0 0 24 24">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
        <span class="ai-scrolling-benefits-text-{{ ai_gen_id }}">Free shipping on orders over $50</span>
      </div>
      <div class="ai-scrolling-benefits-item-{{ ai_gen_id }}">
        <div class="ai-scrolling-benefits-icon-{{ ai_gen_id }}">
          <svg viewBox="0 0 24 24">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
        <span class="ai-scrolling-benefits-text-{{ ai_gen_id }}">30-day money back guarantee</span>
      </div>
      <div class="ai-scrolling-benefits-item-{{ ai_gen_id }}">
        <div class="ai-scrolling-benefits-icon-{{ ai_gen_id }}">
          <svg viewBox="0 0 24 24">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
        <span class="ai-scrolling-benefits-text-{{ ai_gen_id }}">Secure checkout</span>
      </div>
      <div class="ai-scrolling-benefits-item-{{ ai_gen_id }}">
        <div class="ai-scrolling-benefits-icon-{{ ai_gen_id }}">
          <svg viewBox="0 0 24 24">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
        <span class="ai-scrolling-benefits-text-{{ ai_gen_id }}">24/7 customer support</span>
      </div>
    {% endif %}
  </div>
</scrolling-benefits-{{ ai_gen_id }}>

<script>
  (function() {
    class ScrollingBenefits{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
      }

      connectedCallback() {
        this.track = this.querySelector('.ai-scrolling-benefits-track-{{ ai_gen_id }}');
      }
    }

    customElements.define('scrolling-benefits-{{ ai_gen_id }}', ScrollingBenefits{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Scrolling benefits",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Benefits"
    },
    {
      "type": "text",
      "id": "benefit_1",
      "label": "Benefit 1",
      "default": "Free shipping on orders over $50"
    },
    {
      "type": "text",
      "id": "benefit_2",
      "label": "Benefit 2",
      "default": "30-day money back guarantee"
    },
    {
      "type": "text",
      "id": "benefit_3",
      "label": "Benefit 3",
      "default": "Secure checkout"
    },
    {
      "type": "text",
      "id": "benefit_4",
      "label": "Benefit 4",
      "default": "24/7 customer support"
    },
    {
      "type": "header",
      "content": "Icon"
    },
    {
      "type": "select",
      "id": "icon_style",
      "label": "Icon style",
      "options": [
        {
          "value": "checkmark",
          "label": "Checkmark"
        },
        {
          "value": "star",
          "label": "Star"
        },
        {
          "value": "heart",
          "label": "Heart"
        },
        {
          "value": "truck",
          "label": "Truck"
        },
        {
          "value": "shield",
          "label": "Shield"
        },
        {
          "value": "gift",
          "label": "Gift"
        }
      ],
      "default": "checkmark"
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 16,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Icon size",
      "default": 20
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icon color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Text"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 14
    },
    {
      "type": "select",
      "id": "text_weight",
      "label": "Text weight",
      "options": [
        {
          "value": "400",
          "label": "Regular"
        },
        {
          "value": "500",
          "label": "Medium"
        },
        {
          "value": "600",
          "label": "Semibold"
        },
        {
          "value": "700",
          "label": "Bold"
        }
      ],
      "default": "400"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "gap",
      "min": 20,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Gap between items",
      "default": 60
    },
    {
      "type": "range",
      "id": "icon_text_gap",
      "min": 4,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Gap between icon and text",
      "default": 8
    },
    {
      "type": "range",
      "id": "padding_vertical",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Vertical padding",
      "default": 15
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f9f8f6"
    },
    {
      "type": "header",
      "content": "Animation"
    },
    {
      "type": "range",
      "id": "scroll_speed",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "s",
      "label": "Scroll speed",
      "default": 30
    }
  ],
  "presets": [
    {
      "name": "Scrolling benefits"
    }
  ]
}
{% endschema %}
