{% doc %}
  @prompt
    Create a mobile-first “Who it’s for” section WITHOUT a visible title.
    
    Layout & behavior:
    - Horizontal manual scroll only (no auto-play), with scroll-snap.
    - Show ~1.15 items in viewport on mobile so the next item “peeks”.
    - Each item is a small vertical card: icon on top, 1-line label below.
    - Gap between items: 10–14px. Rounded corners (6–10px). No heavy borders or shadows.
    - Truncate long labels with ellipsis.
    - Full RTL support; in RTL the peek is on the right (leading edge).
    - Optional desktop: show 4–6 items per row without scroll.
    
    Design controls (theme editor):
    - Section background color (allow merchant to choose; default: transparent/white).
    - Section padding (top/bottom).
    - Icon size (20–32px).
    - Text size (label small).
    - Item width (% of viewport; default 85% for mobile).
    
    Schema (blocks & dynamic sources):
    - Repeater of item blocks. Each block has:
      • Icon: file picker from /assets (SVG preferred, outline, currentColor)
      • Label: text input WITH dynamic source enabled so merchant can bind a product metafield
    - Add a note in the settings: “Use dynamic source to bind the label from a product metafield (e.g., namespace 'custom', key 'who_for_1').”
    - Allow up to 10 blocks.
    
    Accessibility:
    - The outer list has aria-roledescription="carousel" and aria-label="Who it’s for".
    - Each item has aria-label with its label text.
    
    Implementation details:
    - Container: `display:flex; overflow-x:auto; scroll-snap-type:x mandatory; -webkit-overflow-scrolling:touch; padding-inline:16px;`
    - Item: `flex:0 0 85%; scroll-snap-align:center;`
    - Hide scrollbars; keep keyboard focus visible.
    - In RTL: set the scroller to `direction:ltr` and each card to `direction:rtl` so text remains RTL but scroll feels natural.
    
    Default Hebrew examples (can be replaced via metafields):
    • שיער עדין ונשיר
    • עייפות מתמשכת
    • עדין לקיבה
    • טבעוני/ת
    • כשר
    
{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-who-for-section-{{ ai_gen_id }} {
    background-color: {{ block.settings.section_background }};
    padding-top: {{ block.settings.padding_top }}px;
    padding-bottom: {{ block.settings.padding_bottom }}px;
  }

  .ai-who-for-container-{{ ai_gen_id }} {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    gap: {{ block.settings.item_gap }}px;
    padding-inline: 16px;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .ai-who-for-container-{{ ai_gen_id }}::-webkit-scrollbar {
    display: none;
  }

  .ai-who-for-container-{{ ai_gen_id }}:focus-visible {
    outline: 2px solid {{ block.settings.icon_color }};
    outline-offset: 2px;
  }

  .ai-who-for-item-{{ ai_gen_id }} {
    flex: 0 0 {{ block.settings.item_width }}%;
    scroll-snap-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px 12px;
    background-color: {{ block.settings.item_background }};
    border-radius: {{ block.settings.item_border_radius }}px;
    text-align: center;
    min-height: 100px;
  }

  .ai-who-for-icon-{{ ai_gen_id }} {
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: {{ block.settings.icon_color }};
  }

  .ai-who-for-icon-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .ai-who-for-label-{{ ai_gen_id }} {
    font-size: {{ block.settings.text_size }}px;
    color: {{ block.settings.text_color }};
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-word;
  }

  @media screen and (min-width: 750px) {
    .ai-who-for-container-{{ ai_gen_id }} {
      display: grid;
      grid-template-columns: repeat({{ block.settings.desktop_columns }}, 1fr);
      overflow-x: visible;
      scroll-snap-type: none;
      padding-inline: 0;
      max-width: 1200px;
      margin: 0 auto;
    }

    .ai-who-for-item-{{ ai_gen_id }} {
      flex: none;
      scroll-snap-align: none;
    }
  }

  [dir="rtl"] .ai-who-for-container-{{ ai_gen_id }} {
    direction: ltr;
  }

  [dir="rtl"] .ai-who-for-item-{{ ai_gen_id }} {
    direction: rtl;
  }
{% endstyle %}

<who-for-carousel-{{ ai_gen_id }} {{ block.shopify_attributes }}>
  <div class="ai-who-for-section-{{ ai_gen_id }}">
    <div
      class="ai-who-for-container-{{ ai_gen_id }}"
      role="region"
      aria-roledescription="carousel"
      aria-label="Who it's for"
      tabindex="0"
    >
      {% if block.settings.icon_1 != blank or block.settings.label_1 != blank %}
        <div class="ai-who-for-item-{{ ai_gen_id }}" aria-label="{{ block.settings.label_1 }}">
          {% if block.settings.icon_1 != blank %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <img
                src="{{ block.settings.icon_1 | file_url }}"
                alt=""
                loading="lazy"
                width="{{ block.settings.icon_size }}"
                height="{{ block.settings.icon_size }}"
              >
            </div>
          {% else %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
          {% endif %}
          {% if block.settings.label_1 != blank %}
            <div class="ai-who-for-label-{{ ai_gen_id }}">{{ block.settings.label_1 }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% if block.settings.icon_2 != blank or block.settings.label_2 != blank %}
        <div class="ai-who-for-item-{{ ai_gen_id }}" aria-label="{{ block.settings.label_2 }}">
          {% if block.settings.icon_2 != blank %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <img
                src="{{ block.settings.icon_2 | file_url }}"
                alt=""
                loading="lazy"
                width="{{ block.settings.icon_size }}"
                height="{{ block.settings.icon_size }}"
              >
            </div>
          {% else %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
          {% endif %}
          {% if block.settings.label_2 != blank %}
            <div class="ai-who-for-label-{{ ai_gen_id }}">{{ block.settings.label_2 }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% if block.settings.icon_3 != blank or block.settings.label_3 != blank %}
        <div class="ai-who-for-item-{{ ai_gen_id }}" aria-label="{{ block.settings.label_3 }}">
          {% if block.settings.icon_3 != blank %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <img
                src="{{ block.settings.icon_3 | file_url }}"
                alt=""
                loading="lazy"
                width="{{ block.settings.icon_size }}"
                height="{{ block.settings.icon_size }}"
              >
            </div>
          {% else %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
          {% endif %}
          {% if block.settings.label_3 != blank %}
            <div class="ai-who-for-label-{{ ai_gen_id }}">{{ block.settings.label_3 }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% if block.settings.icon_4 != blank or block.settings.label_4 != blank %}
        <div class="ai-who-for-item-{{ ai_gen_id }}" aria-label="{{ block.settings.label_4 }}">
          {% if block.settings.icon_4 != blank %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <img
                src="{{ block.settings.icon_4 | file_url }}"
                alt=""
                loading="lazy"
                width="{{ block.settings.icon_size }}"
                height="{{ block.settings.icon_size }}"
              >
            </div>
          {% else %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
          {% endif %}
          {% if block.settings.label_4 != blank %}
            <div class="ai-who-for-label-{{ ai_gen_id }}">{{ block.settings.label_4 }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% if block.settings.icon_5 != blank or block.settings.label_5 != blank %}
        <div class="ai-who-for-item-{{ ai_gen_id }}" aria-label="{{ block.settings.label_5 }}">
          {% if block.settings.icon_5 != blank %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <img
                src="{{ block.settings.icon_5 | file_url }}"
                alt=""
                loading="lazy"
                width="{{ block.settings.icon_size }}"
                height="{{ block.settings.icon_size }}"
              >
            </div>
          {% else %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
          {% endif %}
          {% if block.settings.label_5 != blank %}
            <div class="ai-who-for-label-{{ ai_gen_id }}">{{ block.settings.label_5 }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% if block.settings.icon_6 != blank or block.settings.label_6 != blank %}
        <div class="ai-who-for-item-{{ ai_gen_id }}" aria-label="{{ block.settings.label_6 }}">
          {% if block.settings.icon_6 != blank %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <img
                src="{{ block.settings.icon_6 | file_url }}"
                alt=""
                loading="lazy"
                width="{{ block.settings.icon_size }}"
                height="{{ block.settings.icon_size }}"
              >
            </div>
          {% else %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
          {% endif %}
          {% if block.settings.label_6 != blank %}
            <div class="ai-who-for-label-{{ ai_gen_id }}">{{ block.settings.label_6 }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% if block.settings.icon_7 != blank or block.settings.label_7 != blank %}
        <div class="ai-who-for-item-{{ ai_gen_id }}" aria-label="{{ block.settings.label_7 }}">
          {% if block.settings.icon_7 != blank %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <img
                src="{{ block.settings.icon_7 | file_url }}"
                alt=""
                loading="lazy"
                width="{{ block.settings.icon_size }}"
                height="{{ block.settings.icon_size }}"
              >
            </div>
          {% else %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
          {% endif %}
          {% if block.settings.label_7 != blank %}
            <div class="ai-who-for-label-{{ ai_gen_id }}">{{ block.settings.label_7 }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% if block.settings.icon_8 != blank or block.settings.label_8 != blank %}
        <div class="ai-who-for-item-{{ ai_gen_id }}" aria-label="{{ block.settings.label_8 }}">
          {% if block.settings.icon_8 != blank %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <img
                src="{{ block.settings.icon_8 | file_url }}"
                alt=""
                loading="lazy"
                width="{{ block.settings.icon_size }}"
                height="{{ block.settings.icon_size }}"
              >
            </div>
          {% else %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
          {% endif %}
          {% if block.settings.label_8 != blank %}
            <div class="ai-who-for-label-{{ ai_gen_id }}">{{ block.settings.label_8 }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% if block.settings.icon_9 != blank or block.settings.label_9 != blank %}
        <div class="ai-who-for-item-{{ ai_gen_id }}" aria-label="{{ block.settings.label_9 }}">
          {% if block.settings.icon_9 != blank %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <img
                src="{{ block.settings.icon_9 | file_url }}"
                alt=""
                loading="lazy"
                width="{{ block.settings.icon_size }}"
                height="{{ block.settings.icon_size }}"
              >
            </div>
          {% else %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
          {% endif %}
          {% if block.settings.label_9 != blank %}
            <div class="ai-who-for-label-{{ ai_gen_id }}">{{ block.settings.label_9 }}</div>
          {% endif %}
        </div>
      {% endif %}

      {% if block.settings.icon_10 != blank or block.settings.label_10 != blank %}
        <div class="ai-who-for-item-{{ ai_gen_id }}" aria-label="{{ block.settings.label_10 }}">
          {% if block.settings.icon_10 != blank %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <img
                src="{{ block.settings.icon_10 | file_url }}"
                alt=""
                loading="lazy"
                width="{{ block.settings.icon_size }}"
                height="{{ block.settings.icon_size }}"
              >
            </div>
          {% else %}
            <div class="ai-who-for-icon-{{ ai_gen_id }}">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 16v-4"></path>
                <path d="M12 8h.01"></path>
              </svg>
            </div>
          {% endif %}
          {% if block.settings.label_10 != blank %}
            <div class="ai-who-for-label-{{ ai_gen_id }}">{{ block.settings.label_10 }}</div>
          {% endif %}
        </div>
      {% endif %}
    </div>
  </div>
</who-for-carousel-{{ ai_gen_id }}>

<script>
  (function() {
    class WhoForCarousel{{ ai_gen_id }} extends HTMLElement {
      constructor() {
        super();
      }

      connectedCallback() {
        this.container = this.querySelector('.ai-who-for-container-{{ ai_gen_id }}');
        if (!this.container) return;

        this.setupKeyboardNavigation();
      }

      setupKeyboardNavigation() {
        this.container.addEventListener('keydown', (e) => {
          if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
            e.preventDefault();
            const scrollAmount = this.container.offsetWidth * 0.85;
            const direction = e.key === 'ArrowRight' ? 1 : -1;
            const isRTL = document.dir === 'rtl' || document.documentElement.dir === 'rtl';
            const finalDirection = isRTL ? -direction : direction;
            
            this.container.scrollBy({
              left: scrollAmount * finalDirection,
              behavior: 'smooth'
            });
          }
        });
      }
    }

    customElements.define('who-for-carousel-{{ ai_gen_id }}', WhoForCarousel{{ ai_gen_id }});
  })();
</script>

{% schema %}
{
  "name": "Who it's for",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Section style"
    },
    {
      "type": "color",
      "id": "section_background",
      "label": "Background",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 40
    },
    {
      "type": "header",
      "content": "Item style"
    },
    {
      "type": "color",
      "id": "item_background",
      "label": "Item background",
      "default": "#f9f8f6"
    },
    {
      "type": "range",
      "id": "item_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "item_gap",
      "min": 8,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Gap between items",
      "default": 12
    },
    {
      "type": "range",
      "id": "item_width",
      "min": 70,
      "max": 95,
      "step": 5,
      "unit": "%",
      "label": "Item width (mobile)",
      "default": 85
    },
    {
      "type": "header",
      "content": "Icon"
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 20,
      "max": 32,
      "step": 2,
      "unit": "px",
      "label": "Icon size",
      "default": 24
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icon color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Text"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 13
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Desktop layout"
    },
    {
      "type": "range",
      "id": "desktop_columns",
      "min": 4,
      "max": 6,
      "step": 1,
      "label": "Columns",
      "default": 5
    },
    {
      "type": "paragraph",
      "content": "Use dynamic sources to bind labels from product metafields (e.g., namespace 'custom', key 'who_for_1')."
    },
    {
      "type": "header",
      "content": "Item 1"
    },
    {
      "type": "url",
      "id": "icon_1",
      "label": "Icon (SVG from assets)"
    },
    {
      "type": "text",
      "id": "label_1",
      "label": "Label",
      "default": "שיער עדין ונשיר"
    },
    {
      "type": "header",
      "content": "Item 2"
    },
    {
      "type": "url",
      "id": "icon_2",
      "label": "Icon (SVG from assets)"
    },
    {
      "type": "text",
      "id": "label_2",
      "label": "Label",
      "default": "עייפות מתמשכת"
    },
    {
      "type": "header",
      "content": "Item 3"
    },
    {
      "type": "url",
      "id": "icon_3",
      "label": "Icon (SVG from assets)"
    },
    {
      "type": "text",
      "id": "label_3",
      "label": "Label",
      "default": "עדין לקיבה"
    },
    {
      "type": "header",
      "content": "Item 4"
    },
    {
      "type": "url",
      "id": "icon_4",
      "label": "Icon (SVG from assets)"
    },
    {
      "type": "text",
      "id": "label_4",
      "label": "Label",
      "default": "טבעוני/ת"
    },
    {
      "type": "header",
      "content": "Item 5"
    },
    {
      "type": "url",
      "id": "icon_5",
      "label": "Icon (SVG from assets)"
    },
    {
      "type": "text",
      "id": "label_5",
      "label": "Label",
      "default": "כשר"
    },
    {
      "type": "header",
      "content": "Item 6"
    },
    {
      "type": "url",
      "id": "icon_6",
      "label": "Icon (SVG from assets)"
    },
    {
      "type": "text",
      "id": "label_6",
      "label": "Label"
    },
    {
      "type": "header",
      "content": "Item 7"
    },
    {
      "type": "url",
      "id": "icon_7",
      "label": "Icon (SVG from assets)"
    },
    {
      "type": "text",
      "id": "label_7",
      "label": "Label"
    },
    {
      "type": "header",
      "content": "Item 8"
    },
    {
      "type": "url",
      "id": "icon_8",
      "label": "Icon (SVG from assets)"
    },
    {
      "type": "text",
      "id": "label_8",
      "label": "Label"
    },
    {
      "type": "header",
      "content": "Item 9"
    },
    {
      "type": "url",
      "id": "icon_9",
      "label": "Icon (SVG from assets)"
    },
    {
      "type": "text",
      "id": "label_9",
      "label": "Label"
    },
    {
      "type": "header",
      "content": "Item 10"
    },
    {
      "type": "url",
      "id": "icon_10",
      "label": "Icon (SVG from assets)"
    },
    {
      "type": "text",
      "id": "label_10",
      "label": "Label"
    }
  ],
  "presets": [
    {
      "name": "Who it's for"
    }
  ]
}
{% endschema %}
