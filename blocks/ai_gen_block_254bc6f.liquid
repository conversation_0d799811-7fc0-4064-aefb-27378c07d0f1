{% doc %}
  @prompt
    make a collection list banner with scrolling for mobile
{% enddoc %}
```liquid
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-collection-banner-{{ ai_gen_id }} {
    width: 100%;
    background-color: {{ block.settings.background_color }};
    padding: {{ block.settings.padding_top }}px 0 {{ block.settings.padding_bottom }}px;
  }

  .ai-collection-banner-container-{{ ai_gen_id }} {
    max-width: 100%;
    margin: 0 auto;
  }

  .ai-collection-banner-header-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: {{ block.settings.spacing }}px;
    padding: 0 20px;
  }

  .ai-collection-banner-title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.text_color }};
    margin: 0 0 10px;
  }

  .ai-collection-banner-subtitle-{{ ai_gen_id }} {
    font-size: {{ block.settings.subtitle_size }}px;
    color: {{ block.settings.text_color }};
    opacity: 0.8;
    margin: 0;
  }

  .ai-collection-banner-scroll-wrapper-{{ ai_gen_id }} {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .ai-collection-banner-scroll-wrapper-{{ ai_gen_id }}::-webkit-scrollbar {
    display: none;
  }

  .ai-collection-banner-list-{{ ai_gen_id }} {
    display: flex;
    gap: {{ block.settings.gap }}px;
    padding: 0 20px;
    list-style: none;
    margin: 0;
  }

  .ai-collection-banner-item-{{ ai_gen_id }} {
    flex: 0 0 auto;
    width: {{ block.settings.card_width }}px;
  }

  .ai-collection-banner-card-{{ ai_gen_id }} {
    position: relative;
    display: block;
    text-decoration: none;
    border-radius: {{ block.settings.border_radius }}px;
    overflow: hidden;
    height: {{ block.settings.card_height }}px;
    transition: transform 0.3s ease;
  }

  .ai-collection-banner-card-{{ ai_gen_id }}:hover {
    transform: scale(1.02);
  }

  .ai-collection-banner-image-wrapper-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .ai-collection-banner-image-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ai-collection-banner-placeholder-{{ ai_gen_id }} {
    width: 100%;
    height: 100%;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-collection-banner-placeholder-{{ ai_gen_id }} svg {
    width: 60%;
    height: 60%;
    opacity: 0.3;
  }

  .ai-collection-banner-overlay-{{ ai_gen_id }} {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, {{ block.settings.overlay_opacity | divided_by: 100.0 }}), transparent);
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  .ai-collection-banner-collection-title-{{ ai_gen_id }} {
    color: {{ block.settings.overlay_text_color }};
    font-size: {{ block.settings.collection_title_size }}px;
    margin: 0 0 5px;
    font-weight: 600;
  }

  .ai-collection-banner-collection-count-{{ ai_gen_id }} {
    color: {{ block.settings.overlay_text_color }};
    font-size: {{ block.settings.collection_count_size }}px;
    margin: 0;
    opacity: 0.9;
  }

  .ai-collection-banner-empty-state-{{ ai_gen_id }} {
    text-align: center;
    padding: 40px 20px;
    color: {{ block.settings.text_color }};
    opacity: 0.6;
  }

  @media screen and (max-width: 749px) {
    .ai-collection-banner-item-{{ ai_gen_id }} {
      width: {{ block.settings.card_width_mobile }}px;
    }

    .ai-collection-banner-card-{{ ai_gen_id }} {
      height: {{ block.settings.card_height_mobile }}px;
    }

    .ai-collection-banner-title-{{ ai_gen_id }} {
      font-size: {{ block.settings.title_size | times: 0.8 }}px;
    }

    .ai-collection-banner-subtitle-{{ ai_gen_id }} {
      font-size: {{ block.settings.subtitle_size | times: 0.9 }}px;
    }
  }
{% endstyle %}

<div class="ai-collection-banner-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-collection-banner-container-{{ ai_gen_id }}">
    {% if block.settings.title != blank or block.settings.subtitle != blank %}
      <div class="ai-collection-banner-header-{{ ai_gen_id }}">
        {% if block.settings.title != blank %}
          <h2 class="ai-collection-banner-title-{{ ai_gen_id }}">{{ block.settings.title }}</h2>
        {% endif %}
        {% if block.settings.subtitle != blank %}
          <p class="ai-collection-banner-subtitle-{{ ai_gen_id }}">{{ block.settings.subtitle }}</p>
        {% endif %}
      </div>
    {% endif %}

    {% if block.settings.collections != blank %}
      <div class="ai-collection-banner-scroll-wrapper-{{ ai_gen_id }}">
        <ul class="ai-collection-banner-list-{{ ai_gen_id }}">
          {% for collection in block.settings.collections %}
            <li class="ai-collection-banner-item-{{ ai_gen_id }}">
              <a href="{{ collection.url }}" class="ai-collection-banner-card-{{ ai_gen_id }}">
                <div class="ai-collection-banner-image-wrapper-{{ ai_gen_id }}">
                  {% if collection.featured_image %}
                    <img
                      src="{{ collection.featured_image | image_url: width: 600 }}"
                      alt="{{ collection.featured_image.alt | escape }}"
                      class="ai-collection-banner-image-{{ ai_gen_id }}"
                      loading="lazy"
                      width="600"
                      height="{{ block.settings.card_height }}"
                    >
                  {% else %}
                    <div class="ai-collection-banner-placeholder-{{ ai_gen_id }}">
                      {{ 'collection-1' | placeholder_svg_tag }}
                    </div>
                  {% endif %}
                  <div class="ai-collection-banner-overlay-{{ ai_gen_id }}">
                    <h3 class="ai-collection-banner-collection-title-{{ ai_gen_id }}">{{ collection.title }}</h3>
                    {% if block.settings.show_product_count %}
                      <p class="ai-collection-banner-collection-count-{{ ai_gen_id }}">
                        {{ collection.products_count }}
                        {% if collection.products_count == 1 %}
                          product
                        {% else %}
                          products
                        {% endif %}
                      </p>
                    {% endif %}
                  </div>
                </div>
              </a>
            </li>
          {% endfor %}
        </ul>
      </div>
    {% else %}
      <div class="ai-collection-banner-empty-state-{{ ai_gen_id }}">
        Select collections to display in this banner
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Collection list banner",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Shop by collection"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subheading"
    },
    {
      "type": "collection_list",
      "id": "collections",
      "label": "Collections",
      "limit": 12
    },
    {
      "type": "checkbox",
      "id": "show_product_count",
      "label": "Show product count",
      "default": true
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "card_width",
      "min": 200,
      "max": 400,
      "step": 20,
      "unit": "px",
      "label": "Card width (desktop)",
      "default": 280
    },
    {
      "type": "range",
      "id": "card_width_mobile",
      "min": 150,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Card width (mobile)",
      "default": 220
    },
    {
      "type": "range",
      "id": "card_height",
      "min": 200,
      "max": 500,
      "step": 20,
      "unit": "px",
      "label": "Card height (desktop)",
      "default": 320
    },
    {
      "type": "range",
      "id": "card_height_mobile",
      "min": 150,
      "max": 400,
      "step": 10,
      "unit": "px",
      "label": "Card height (mobile)",
      "default": 240
    },
    {
      "type": "range",
      "id": "gap",
      "min": 8,
      "max": 40,
      "step": 4,
      "unit": "px",
      "label": "Gap between cards",
      "default": 16
    },
    {
      "type": "range",
      "id": "spacing",
      "min": 10,
      "max": 60,
      "step": 5,
      "unit": "px",
      "label": "Spacing below header",
      "default": 30
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Top padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Bottom padding",
      "default": 40
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Card border radius",
      "default": 8
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background",
      "default": "#f9f8f6"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 32
    },
    {
      "type": "range",
      "id": "subtitle_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Subheading size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Overlay"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Overlay opacity",
      "default": 60
    },
    {
      "type": "color",
      "id": "overlay_text_color",
      "label": "Overlay text",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "collection_title_size",
      "min": 14,
      "max": 32,
      "step": 2,
      "unit": "px",
      "label": "Collection title size",
      "default": 20
    },
    {
      "type": "range",
      "id": "collection_count_size",
      "min": 10,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Product count size",
      "default": 14
    }
  ],
  "presets": [
    {
      "name": "Collection list banner"
    }
  ]
}
{% endschema %}
```