{% doc %}
  @prompt
    icon with text section for shipping invvormastion
{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-shipping-info-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: {{ block.settings.spacing }}px;
    padding: {{ block.settings.padding }}px;
    background-color: {{ block.settings.background_color }};
    border-radius: {{ block.settings.border_radius }}px;
  }

  .ai-shipping-info-item-{{ ai_gen_id }} {
    display: flex;
    align-items: center;
    gap: {{ block.settings.icon_text_gap }}px;
  }

  .ai-shipping-info-icon-{{ ai_gen_id }} {
    flex-shrink: 0;
    width: {{ block.settings.icon_size }}px;
    height: {{ block.settings.icon_size }}px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: {{ block.settings.icon_color }};
  }

  .ai-shipping-info-icon-{{ ai_gen_id }} svg {
    width: 100%;
    height: 100%;
  }

  .ai-shipping-info-text-{{ ai_gen_id }} {
    flex-grow: 1;
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.text_size }}px;
    line-height: 1.4;
  }

  @media screen and (max-width: 749px) {
    .ai-shipping-info-{{ ai_gen_id }} {
      padding: {{ block.settings.padding | times: 0.8 }}px;
    }

    .ai-shipping-info-icon-{{ ai_gen_id }} {
      width: {{ block.settings.icon_size | times: 0.9 }}px;
      height: {{ block.settings.icon_size | times: 0.9 }}px;
    }

    .ai-shipping-info-text-{{ ai_gen_id }} {
      font-size: {{ block.settings.text_size | times: 0.9 }}px;
    }
  }
{% endstyle %}

<div class="ai-shipping-info-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  {% if block.settings.show_free_shipping %}
    <div class="ai-shipping-info-item-{{ ai_gen_id }}">
      <div class="ai-shipping-info-icon-{{ ai_gen_id }}">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="1" y="3" width="15" height="13"></rect>
          <polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon>
          <circle cx="5.5" cy="18.5" r="2.5"></circle>
          <circle cx="18.5" cy="18.5" r="2.5"></circle>
        </svg>
      </div>
      <div class="ai-shipping-info-text-{{ ai_gen_id }}">
        {{ block.settings.free_shipping_text }}
      </div>
    </div>
  {% endif %}

  {% if block.settings.show_fast_delivery %}
    <div class="ai-shipping-info-item-{{ ai_gen_id }}">
      <div class="ai-shipping-info-icon-{{ ai_gen_id }}">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="16 3 21 3 21 8"></polyline>
          <line x1="4" y1="20" x2="21" y2="3"></line>
          <polyline points="21 16 21 21 16 21"></polyline>
          <line x1="15" y1="15" x2="21" y2="21"></line>
          <line x1="4" y1="4" x2="9" y2="9"></line>
        </svg>
      </div>
      <div class="ai-shipping-info-text-{{ ai_gen_id }}">
        {{ block.settings.fast_delivery_text }}
      </div>
    </div>
  {% endif %}

  {% if block.settings.show_secure_checkout %}
    <div class="ai-shipping-info-item-{{ ai_gen_id }}">
      <div class="ai-shipping-info-icon-{{ ai_gen_id }}">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
          <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
        </svg>
      </div>
      <div class="ai-shipping-info-text-{{ ai_gen_id }}">
        {{ block.settings.secure_checkout_text }}
      </div>
    </div>
  {% endif %}

  {% if block.settings.show_easy_returns %}
    <div class="ai-shipping-info-item-{{ ai_gen_id }}">
      <div class="ai-shipping-info-icon-{{ ai_gen_id }}">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="1 4 1 10 7 10"></polyline>
          <polyline points="23 20 23 14 17 14"></polyline>
          <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
        </svg>
      </div>
      <div class="ai-shipping-info-text-{{ ai_gen_id }}">
        {{ block.settings.easy_returns_text }}
      </div>
    </div>
  {% endif %}
</div>

{% schema %}
{
  "name": "Shipping information",
  "settings": [
    {
      "type": "header",
      "content": "Shipping features"
    },
    {
      "type": "checkbox",
      "id": "show_free_shipping",
      "label": "Show free shipping",
      "default": true
    },
    {
      "type": "text",
      "id": "free_shipping_text",
      "label": "Free shipping text",
      "default": "Free shipping on orders over $50"
    },
    {
      "type": "checkbox",
      "id": "show_fast_delivery",
      "label": "Show fast delivery",
      "default": true
    },
    {
      "type": "text",
      "id": "fast_delivery_text",
      "label": "Fast delivery text",
      "default": "Fast delivery in 2-3 business days"
    },
    {
      "type": "checkbox",
      "id": "show_secure_checkout",
      "label": "Show secure checkout",
      "default": true
    },
    {
      "type": "text",
      "id": "secure_checkout_text",
      "label": "Secure checkout text",
      "default": "Secure checkout guaranteed"
    },
    {
      "type": "checkbox",
      "id": "show_easy_returns",
      "label": "Show easy returns",
      "default": true
    },
    {
      "type": "text",
      "id": "easy_returns_text",
      "label": "Easy returns text",
      "default": "Easy returns within 30 days"
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 16,
      "max": 48,
      "step": 2,
      "unit": "px",
      "label": "Icon size",
      "default": 24
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 10,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 14
    },
    {
      "type": "range",
      "id": "icon_text_gap",
      "min": 4,
      "max": 24,
      "step": 2,
      "unit": "px",
      "label": "Icon to text spacing",
      "default": 12
    },
    {
      "type": "range",
      "id": "spacing",
      "min": 8,
      "max": 32,
      "step": 2,
      "unit": "px",
      "label": "Item spacing",
      "default": 16
    },
    {
      "type": "range",
      "id": "padding",
      "min": 0,
      "max": 40,
      "step": 4,
      "unit": "px",
      "label": "Padding",
      "default": 16
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Border radius",
      "default": 0
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background",
      "default": "#f9f8f6"
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icon",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#000000"
    }
  ],
  "presets": [
    {
      "name": "Shipping information"
    }
  ]
}
{% endschema %}
