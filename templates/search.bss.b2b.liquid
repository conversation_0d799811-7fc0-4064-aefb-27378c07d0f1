{% layout none %}{% paginate search.results by 1000 %}[{% for item in search.results %}{ "id":{{ item.id | json }},  "title": {{ item.title | json}}, "tags": {{ item.tags | json }},"handle":{{ item.handle | json }},  "variants": [{% for variant in item.variants %}{ "weight_unit": {{ variant.weight_unit | json }}, "id": {{ variant.id | json }},  "weight": {{ variant.weight | json }}, "taxable" : {{ variant.taxable | json}}, "title":{{ variant.title | json }},  "price": {{ variant.price | json }},  "compare_at_price":{{ variant.compare_at_price | json }},  "available": {{ variant.available | json }},  "unit_price":{{variant.unit_price | json}},  "base_measure":{{variant.unit_price_measurement.reference_value | json}},  "quantity_value" :{{variant.unit_price_measurement.quantity_value | json}}}{% unless forloop.last %},{% endunless %}{% endfor %}  ],  "available":{{ item.available | json }},  "compare_at_price_max":{{ item.compare_at_price_max | json }},  "compare_at_price_min":{{ item.compare_at_price_min | json }},  "price":{{ item.price | json }},  "price_max":{{ item.price_max | json }},  "price_min":{{ item.price_min | json }},  "collections": [{% for collection in item.collections %}{{ collection.id }}{% unless forloop.last %},{% endunless %}{% endfor %}  ]}{% unless forloop.last %},{% endunless %}{% endfor %}]{% endpaginate %}

