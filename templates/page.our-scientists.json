/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "about_hero_banner_fBiE8y": {
      "type": "about-hero-banner",
      "blocks": {
        "banner_content_4kwcf4": {
          "type": "banner_content",
          "settings": {
            "banner_image_desktop": "shopify://shop_images/f237609a1432fea89eadeaab63f4bbe9bc35d07b.jpg",
            "banner_image_mobile": "shopify://shop_images/Hero.jpg",
            "sub_title": "מאחורי המדע",
            "title": "המדע והטכנולוגיה מאחורי החדשנות של סקויה"
          }
        }
      },
      "block_order": [
        "banner_content_4kwcf4"
      ],
      "settings": {}
    },
    "section_fPJFH6": {
      "type": "section",
      "blocks": {
        "image_bEkBBb": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/team_page_01.jpg",
            "link": "",
            "image_ratio": "adapt",
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fill",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 12,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "group_76MWVw": {
          "type": "group",
          "settings": {
            "link": "",
            "group-class": "our-scientists-intro-content",
            "open_in_new_tab": false,
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "center",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-end",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 12,
            "width": "custom",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "toggle_overlay": false,
            "overlay_color": "#00000026",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "text_nCUcM6": {
              "type": "text",
              "name": "t:names.heading",
              "settings": {
                "text": "<h3><strong>איך חדשנות מדעית הפכה לחזון בריאותי</strong></h3>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "2rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "text_RrV8nn": {
              "type": "text",
              "settings": {
                "text": "<p>היכולת לתרגם ידע מדעי לפתרונות מוחשיים בתחום הבריאות עומדת בלב העשייה שלנו.</p><p>באמצעות טכנולוגיות מתקדמות להובלה תאית, בשילוב מחקר קליני וביוכימי עדכני, הפכנו רעיונות מורכבים לפורמולות נגישות, מדויקות ויעילות.</p><p>המטרה: לאפשר לכל אדם גישה לתוספים שפועלים באמת, על בסיס מדע, לא רק הבטחה.</p>",
                "width": "fit-content",
                "max_width": "narrow",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "1rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 34,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "button_rG9hzd": {
              "type": "button",
              "disabled": true,
              "settings": {
                "label": "Shop now",
                "link": "shopify://collections/all",
                "open_in_new_tab": false,
                "style_class": "button",
                "width": "fit-content",
                "custom_width": 100,
                "width_mobile": "fit-content",
                "custom_width_mobile": 100,
                "custom_css_class": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_nCUcM6",
            "text_RrV8nn",
            "button_rG9hzd"
          ]
        }
      },
      "block_order": [
        "image_bEkBBb",
        "group_76MWVw"
      ],
      "custom_css": [],
      "name": "t:names.image_with_text",
      "settings": {
        "content_direction": "row",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "flex-start",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 82,
        "section_width": "full-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "scheme-794685bf-6ab5-4147-93f6-2ef40a4b25c2",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 60,
        "padding-block-end": 100,
        "custom_css_class": "about__healthSecOneWrapper"
      }
    },
    "sp_our_stats_count_6w7Jh7": {
      "type": "sp-our-stats-count",
      "blocks": {
        "card_pXykNn": {
          "type": "card",
          "settings": {
            "number": "200+ ",
            "label": "מחקרים מדעיים בלתי תלויים שפורסמו בעשור האחרון בלבד, וכולם מצביעים על היתרונות בשילוב תוספי תזונה במתן ליפוזומלי לשיפור ספיגה, יציבות והובלה ממוקדת של שלל רכיבים פעילים."
          }
        },
        "card_ajF7P3": {
          "type": "card",
          "settings": {
            "number": "100+ ",
            "label": "שנות ניסיון מצטברות של מחקר מדעי בקרב הסגל המדעי בסקויה עם התמחויות מעולם התזונה, רפואה, כימיה, ביולוגיה, ופרמקולוגיה."
          }
        },
        "card_i9ghMP": {
          "type": "card",
          "settings": {
            "number": "500",
            "label": "ננומטר, גודל טיפוסי של ליפוזומים אשר קטן פי 15 מתאי דם אדומים על מנת לאפשר גישה וחדירה ברמה התאית."
          }
        },
        "card_KU4KKF": {
          "type": "card",
          "settings": {
            "number": "X40",
            "label": "שיפור יכולות ספיגה עבור כורכומין כאשר נצרך במתן ליפוזומלי ביחס לתוסף קונבנציונלי."
          }
        }
      },
      "block_order": [
        "card_pXykNn",
        "card_ajF7P3",
        "card_i9ghMP",
        "card_KU4KKF"
      ],
      "custom_css": [
        "{direction: rtl;}"
      ],
      "name": "SP Our Stats Count",
      "settings": {
        "title": "המספרים שמוכיחים את ההבדל",
        "description": "מאחורי כל פורמולה של סקויה עומדים הרים של נתונים מדויקים ומדדים קליניים ברורים. לא תחושות ולא השערות.\nאנחנו מודדים ספיגה ביולוגית, יציבות מולקולרית ויעילות מערכתית ומשווים לתוספים רגילים כדי להבטיח שההבדל יהיה לא רק מורגש, אלא מדיד.",
        "button_label": "לכל המוצרים",
        "button_link": "#",
        "bg_color": "rgba(0,0,0,0)",
        "button_bg": "#111111",
        "button_text": "#ffffff"
      }
    },
    "scientists_experts_new": {
      "type": "scientists-experts",
      "blocks": {
        "expert_1": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/Frame_57.jpg",
            "expert_name": "ד”ר רועי רוטנברג",
            "expert_title": "סמנכ”ל המו”פ של סקויה",
            "expert_description": "בעל PhD בכימיה, מוביל את המחקר והפיתוח של מוצרי סקויה. מומחה לשחרור מבוקר, אוטומציה, וחדשנות מדעית."
          }
        },
        "expert_2": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/Frame_58.jpg",
            "expert_name": "פרופ’ איתמר גרוטו",
            "expert_title": "רופא מומחה בבריאות הציבור",
            "expert_description": "מייעץ לסקויה בנושאי פיתוחים עסקיים, והשקת מוצרים העונים על אמות מידה בינלאומיות."
          }
        },
        "expert_3": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/Frame_60.jpg",
            "expert_name": "ד”ר אבישי גביש",
            "expert_title": "רופא, בעל PhD בביולוגיה חישובית",
            "expert_description": "משמש כחוקר בסטנפורד. הוא מומחה בגנומיקה, ויועץ בכיר לפיתוחים מדעיים ועסקיים בסקויה."
          }
        },
        "expert_4": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/Frame_63_1.jpg",
            "expert_name": "פרופ’ משה גביש",
            "expert_title": "פרופסור לרפואה בטכניון",
            "expert_description": "מומחה לפרמקולוגיה, מוביל מחקר חדשני על ליפוזומים כווקטורים תרופתיים, ובעל ניסיון אקדמי של מעל 40 שנה."
          }
        },
        "expert_5": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/Frame_59_1.jpg",
            "expert_name": "פרופ’ אבי וייצמן",
            "expert_title": "פרופסור לפסיכיאטריה",
            "expert_description": "מומחה עולמי בפסיכופרמקולוגיה, משלב ניסיון קליני ומחקרי להבנת ההשפעות הנוירו-כימיות של רכיבים תזונתיים ותרופתיים."
          }
        },
        "expert_6": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/Frame_65.jpg",
            "expert_name": "רמי מולא",
            "expert_title": "דיאטן קליני מוסמך",
            "expert_description": "מתמחה בתזונת ספורט והתאמה אישית של תוכניות תזונה עם שילוב של ניסיון קליני וגישה מדידה לשיפור תפקוד ובריאות."
          }
        },
        "expert_8": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/Frame_64.jpg",
            "expert_name": "רון יפה",
            "expert_title": "נטורופת בכיר",
            "expert_description": "מרצה בינלאומי המתמחה ברפואה פונקציונלית, ביוכימיה ופרמקולוגיה. מפתח תוספים מותאמים אישית בגישה שמשלבת מדע והוליסטיקה."
          }
        },
        "scientist_expert_iKb9Q8": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/WhatsApp_Image_2025-10-05_at_07.05.50_c6e76a31-5384-4647-84f6-949b9ed95d3e.jpg",
            "expert_name": "ד״ר מרים מפרע",
            "expert_title": "רופאת משפחה",
            "expert_description": "רופאת משפחה ובעלת תואר שני בהתמחות לרפואת השמנה מאוניברסיטת תל אביב. עוסקת בטיפול מקצועי ורב־תחומי במטופלים עם עודף משקל, מדריכה סטודנטים ומתמחים ברפואה, ומובילה את הכשרת דור העתיד בתחום."
          }
        },
        "scientist_expert_PJeyWt": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/a1ed56dbc0a4d5cf976cc97a227c2840.jpg",
            "expert_name": "ד\"ר ענבל חנוכה כץ",
            "expert_title": "חוקרת בכירה",
            "expert_description": "בעלת PhD בהנדסת ביוטכנולוגיה ומזון, אחראית על תחומי האנליטיקה והפורמולציה כחלק מצוות המו\"פ."
          }
        },
        "scientist_expert_hXpptP": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/20240220_154710_2.jpg",
            "expert_name": "אליס פופיק",
            "expert_title": "חוקרת",
            "expert_description": "מהנדסת ביוטכנולוגיה ומזון (B.Sc., טכניון), אחראית על פיתוח, ייצור ושיפור פורמולציות בצוות מחקר ופיתוח."
          }
        },
        "scientist_expert_rc3rt9": {
          "type": "scientist_expert",
          "settings": {
            "expert_image": "shopify://shop_images/IMG_0479_1.jpg",
            "expert_name": "רוזין שחאדה",
            "expert_title": "עוזרת מחקר",
            "expert_description": "בעלת תואר כפול בביולוגיה ובמדעי הרפואה. ניסיונה הקודם בעבודה פורמולטורית עם תוספי תזונה ואפיון אנליטי של ויטמינים במעבדות המחקר של בית החולים רמב״ם מהווה תרומה משמעותית לפעילותה בסקויה"
          }
        }
      },
      "block_order": [
        "expert_1",
        "expert_2",
        "expert_3",
        "expert_4",
        "expert_5",
        "expert_6",
        "expert_8",
        "scientist_expert_iKb9Q8",
        "scientist_expert_PJeyWt",
        "scientist_expert_hXpptP",
        "scientist_expert_rc3rt9"
      ],
      "name": "Scientists Experts",
      "settings": {
        "section_title": "המומחים סומכים עלינו, גם אתם יכולים",
        "color_scheme": "scheme-794685bf-6ab5-4147-93f6-2ef40a4b25c2"
      }
    },
    "section_AqrUGQ": {
      "type": "section",
      "blocks": {
        "text_Xa9krt": {
          "type": "text",
          "settings": {
            "text": "<p><strong>טכנולוגיית הליפוזום מאפשרת ספיגה גבוהה ישירות לתאים, משפרת את יעילות המוצרים ומבטיחה שימוש נוח וידידותי לגוף.</strong></p>",
            "width": "100%",
            "max_width": "none",
            "alignment": "right",
            "type_preset": "custom",
            "font": "var(--font-body--family)",
            "font_size": "2rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground-heading)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 24,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": "text__headingWrapper"
          },
          "blocks": {}
        },
        "group_GeHcnQ": {
          "type": "group",
          "name": "t:names.group",
          "settings": {
            "link": "#",
            "group-class": "split-showcase-header-link",
            "open_in_new_tab": false,
            "content_direction": "row",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-end",
            "vertical_alignment": "center",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-start",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 12,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "toggle_overlay": false,
            "overlay_color": "#00000026",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "text_QTbyzz": {
              "type": "text",
              "name": "t:names.text",
              "settings": {
                "text": "<p>מאחורי המדע</p>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "1rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "icon_WdTbwj": {
              "type": "icon",
              "name": "t:names.icon",
              "settings": {
                "icon": "price_tag",
                "image_upload": "shopify://shop_images/arr.png",
                "width": 30,
                "link": "",
                "open_in_new_tab": false
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_QTbyzz",
            "icon_WdTbwj"
          ]
        }
      },
      "block_order": [
        "text_Xa9krt",
        "group_GeHcnQ"
      ],
      "custom_css": [
        "{direction: rtl; padding: 0 120px;}",
        "@media screen and (max-width: 767px) {{padding: 0 16px; } .pull__quoteTextWrapper > .custom-section-content > .section-content-wrapper {padding: 15px 0 40px; } .text__headingWrapper {padding: 0; } .text__headingWrapper p {font-size: 24px; } .group-block-content {flex-flow: row nowrap; justify-content: flex-start; display: none; }}",
        ".split-showcase-header-link {width: fit-content; margin-left: auto;}"
      ],
      "name": "Split Showcase Header",
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-end",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "flex-end",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 0,
        "section_width": "full-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "scheme-2",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 100,
        "padding-block-end": 40,
        "custom_css_class": "pull__quoteTextWrapper"
      }
    },
    "section_BqY9HD": {
      "type": "section",
      "blocks": {
        "group_qjb8H7": {
          "type": "group",
          "settings": {
            "link": "",
            "group-class": "",
            "open_in_new_tab": false,
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "center",
            "vertical_alignment": "center",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-end",
            "vertical_alignment_flex_direction_column": "flex-start",
            "gap": 0,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fill",
            "custom_height": 55,
            "inherit_color_scheme": false,
            "color_scheme": "scheme-5",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "solid",
            "border_width": 0.5,
            "border_opacity": 37,
            "border_radius": 0,
            "toggle_overlay": false,
            "overlay_color": "#f8f8f5",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 47,
            "padding-block-end": 24,
            "padding-inline-start": 47,
            "padding-inline-end": 24
          },
          "blocks": {
            "text_ganrAE": {
              "type": "text",
              "settings": {
                "text": "<p><strong>ספיגה גבוהה ויעילות מקסימלית</strong></p>",
                "width": "100%",
                "max_width": "normal",
                "alignment": "right",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "1.125rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground-heading)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "text_XtF4cG": {
              "type": "text",
              "name": "t:names.text",
              "settings": {
                "text": "<p>הליפוזום מקדם ספיגה ממוקדת ויעילה יותר, לעומת תוספים סטנדרטיים בעלי זמינות חלקית.</p>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "0.875rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 14,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_ganrAE",
            "text_XtF4cG"
          ]
        },
        "group_QJ3ePd": {
          "type": "group",
          "settings": {
            "link": "",
            "group-class": "",
            "open_in_new_tab": false,
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "center",
            "vertical_alignment": "center",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-end",
            "vertical_alignment_flex_direction_column": "flex-start",
            "gap": 0,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fill",
            "custom_height": 55,
            "inherit_color_scheme": false,
            "color_scheme": "scheme-5",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "solid",
            "border_width": 0.5,
            "border_opacity": 37,
            "border_radius": 0,
            "toggle_overlay": false,
            "overlay_color": "#f8f8f5",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 47,
            "padding-block-end": 24,
            "padding-inline-start": 47,
            "padding-inline-end": 24
          },
          "blocks": {
            "text_PEF3RQ": {
              "type": "text",
              "settings": {
                "text": "<p><strong>הגנה על הרכיבים הפעילים</strong></p>",
                "width": "100%",
                "max_width": "normal",
                "alignment": "right",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "1.125rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground-heading)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "text_zpw9pJ": {
              "type": "text",
              "name": "t:names.text",
              "settings": {
                "text": "<p>המעטפת הליפוזומלית מגנה על הרכיבים הפעילים ומסייעת בהובלה מדויקת לאתרים הרצויים בגוף.</p>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "0.875rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 14,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 10,
                "custom_css_class": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_PEF3RQ",
            "text_zpw9pJ"
          ]
        },
        "group_gpTLtK": {
          "type": "group",
          "settings": {
            "link": "",
            "group-class": "",
            "open_in_new_tab": false,
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "center",
            "vertical_alignment": "center",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-end",
            "vertical_alignment_flex_direction_column": "flex-start",
            "gap": 0,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fill",
            "custom_height": 55,
            "inherit_color_scheme": false,
            "color_scheme": "scheme-5",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "solid",
            "border_width": 0.5,
            "border_opacity": 37,
            "border_radius": 0,
            "toggle_overlay": false,
            "overlay_color": "#f8f8f5",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 47,
            "padding-block-end": 24,
            "padding-inline-start": 47,
            "padding-inline-end": 24
          },
          "blocks": {
            "text_QwfDay": {
              "type": "text",
              "settings": {
                "text": "<p><strong>ידידותי למערכת העיכול</strong></p>",
                "width": "100%",
                "max_width": "normal",
                "alignment": "right",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "1.125rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground-heading)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "text_ttazKi": {
              "type": "text",
              "name": "t:names.text",
              "settings": {
                "text": "<p>הממברנה של הליפוזום מסייעת להפחתת גירוי במערכת העיכול ותומכת בספיגה יעילה, בלי להתפשר על נוחות השימוש.</p>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "0.875rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 14,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 10,
                "custom_css_class": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_QwfDay",
            "text_ttazKi"
          ]
        }
      },
      "block_order": [
        "group_qjb8H7",
        "group_QJ3ePd",
        "group_gpTLtK"
      ],
      "custom_css": [
        "{direction: rtl; padding: 0 120px;}",
        "@media screen and (max-width: 767px) {{padding: 0 16px; }}"
      ],
      "name": "t:names.split_showcase",
      "settings": {
        "content_direction": "row",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "flex-start",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 0,
        "section_width": "full-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "scheme-2",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 0,
        "padding-block-end": 40,
        "custom_css_class": "split__showCaseWrapper"
      }
    },
    "section_d8rLGf": {
      "type": "section",
      "blocks": {
        "video_GYNWTM": {
          "type": "video",
          "name": "t:names.video",
          "settings": {
            "source": "uploaded",
            "video": "shopify://files/videos/video hero2.mp4",
            "video_url": "",
            "video_autoplay": false,
            "video_loop": true,
            "alt": "",
            "custom_width": 100,
            "custom_width_mobile": 100,
            "aspect_ratio": "16/9",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 12,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "group_cTpQxT": {
          "type": "group",
          "name": "Caption",
          "disabled": true,
          "settings": {
            "link": "",
            "group-class": "",
            "open_in_new_tab": false,
            "content_direction": "row",
            "vertical_on_mobile": true,
            "horizontal_alignment": "space-between",
            "vertical_alignment": "flex-end",
            "align_baseline": true,
            "horizontal_alignment_flex_direction_column": "flex-start",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 12,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "toggle_overlay": false,
            "overlay_color": "#00000026",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "text_anPdiC": {
              "type": "text",
              "name": "t:names.text",
              "settings": {
                "text": "<p>Take a look behind the scenes of our latest product launch.</p>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "rte",
                "font": "var(--font-body--family)",
                "font_size": "",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "button_pfUp9L": {
              "type": "button",
              "name": "t:names.button",
              "settings": {
                "label": "Discover the collection",
                "link": "shopify://collections/all",
                "open_in_new_tab": false,
                "style_class": "link",
                "width": "fit-content",
                "custom_width": 100,
                "width_mobile": "fit-content",
                "custom_width_mobile": 100,
                "custom_css_class": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_anPdiC",
            "button_pfUp9L"
          ]
        }
      },
      "block_order": [
        "video_GYNWTM",
        "group_cTpQxT"
      ],
      "custom_css": [
        "{padding: 0 120px;}",
        "@media (max-width: 768px) {{padding: 0; } deferred-media {--border-radius: 0 !important; }}",
        ".scientists__videoSecWrapper .button {padding: 0 !important;}"
      ],
      "name": "Hero Video",
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "flex-start",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 0,
        "section_width": "full-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 40,
        "padding-block-end": 60,
        "custom_css_class": "scientists__videoSecWrapper"
      }
    },
    "custom_info_blocks_VkGgkD": {
      "type": "custom-info-blocks",
      "blocks": {
        "info_block_QHTRVb": {
          "type": "info_block",
          "settings": {
            "block_title": "הגנה ושימור הרכיבים הפעילים",
            "block_description": "הליפוזום עוטף את הרכיבים במעטפת שומנית המגנה עליהם ומשמרת את יעילותם בדרכם לגוף."
          }
        },
        "info_block_8pKVVB": {
          "type": "info_block",
          "settings": {
            "block_title": "ספיגה אופטימלית",
            "block_description": "נספג בקלות במעי ומעביר את הרכיבים ישירות למחזור הדם ביעילות מבוקרת."
          }
        },
        "info_block_3gy9jY": {
          "type": "info_block",
          "settings": {
            "block_title": "שחרור ממוקד",
            "block_description": "משחרר את הרכיבים ישירות לתאים, משפר זמינות ביולוגית ומבטיח תוצאות מקסימליות."
          }
        }
      },
      "block_order": [
        "info_block_QHTRVb",
        "info_block_8pKVVB",
        "info_block_3gy9jY"
      ],
      "custom_css": [
        "{direction: rtl;}"
      ],
      "name": "Info Blocks Section",
      "settings": {
        "section_title": "איך הליפוזום מבטיח ספיגה מקסימלית ויעילות גבוהה"
      }
    },
    "sp_animated_boxes_xtE3gM": {
      "type": "sp-animated-boxes",
      "blocks": {
        "box_BnB7ya": {
          "type": "box",
          "settings": {
            "box_image": "shopify://shop_images/Frame_1984077419.png",
            "title": "ערכים ומעורבות חברתית",
            "text": "הובלת את תחום הבריאות באמצעות טכנולוגיה ליפוזומלית מתקדמת, המספקת ספיגה ביולוגית מיטבית ויעילות מוכחת מדעית.",
            "title_color": "#ffffff",
            "text_color": "#ffffff",
            "title_size": 24,
            "text_size": 16,
            "box_padding": 20,
            "box_radius": 8
          }
        },
        "box_493K8Q": {
          "type": "box",
          "settings": {
            "box_image": "shopify://shop_images/Frame_1984077419.png",
            "title": "ערכים ומעורבות חברתית",
            "text": "הובלת את תחום הבריאות באמצעות טכנולוגיה ליפוזומלית מתקדמת, המספקת ספיגה ביולוגית מיטבית ויעילות מוכחת מדעית.",
            "title_color": "#ffffff",
            "text_color": "#ffffff",
            "title_size": 24,
            "text_size": 16,
            "box_padding": 20,
            "box_radius": 8
          }
        }
      },
      "block_order": [
        "box_BnB7ya",
        "box_493K8Q"
      ],
      "disabled": true,
      "name": "SP Animated Boxes",
      "settings": {
        "section_title": "",
        "section_description": "",
        "bg_color": "#ffffff",
        "padding_top": 40,
        "padding_sides": 120,
        "padding_bottom": 60,
        "gap": 24
      }
    },
    "animated_cards_4fDwfq": {
      "type": "animated-cards",
      "blocks": {
        "product_card_yx3BqC": {
          "type": "product_card",
          "settings": {
            "product": "lipo-for-me",
            "product_handle": "",
            "open_in_new_tab": false,
            "custom_image": "shopify://shop_images/RBS02033.jpg",
            "custom_title": "",
            "custom_price": "",
            "custom_description": "הטריו המנצח לחיים בריאים עם שלושה כוכבים מהסדרה שלנו ליצירת חוויית בריאות מקיפה",
            "show_price": true,
            "badge_text": "הכי פופלרי"
          }
        },
        "product_card_yNe66j": {
          "type": "product_card",
          "settings": {
            "product": "lipo-for-us",
            "product_handle": "",
            "open_in_new_tab": false,
            "custom_image": "shopify://shop_images/RBS02037.jpg",
            "custom_title": "Lipo For US",
            "custom_price": "",
            "custom_description": "הכירו את ערכת Lipo for Us ערכה זוגית מושלמת שמביאה אתכם יחד לשמירה על שגרה",
            "show_price": true,
            "badge_text": "הכי פופולרי"
          }
        }
      },
      "block_order": [
        "product_card_yx3BqC",
        "product_card_yNe66j"
      ],
      "name": "Animated Cards",
      "settings": {
        "bg_color": "#ffffff",
        "padding_top": 60,
        "padding_bottom": 60,
        "card_bg_color": "#ffffff",
        "card_radius": 12,
        "description_size": 16
      }
    },
    "section_QRD9Rx": {
      "type": "section",
      "blocks": {
        "text_bWqt3X": {
          "type": "text",
          "settings": {
            "text": "<p><strong>מעבדת סקויה משלבת מדע יישומי, ביוטכנולוגיה מתקדמת וסטנדרטים קליניים לפיתוח פורמולות ליפוזומליות עם ספיגה וזמינות ביולוגית גבוהה, תוך בקרה קפדנית וציוד מדעי מתקדם.</strong><br/></p>",
            "width": "100%",
            "max_width": "none",
            "alignment": "right",
            "type_preset": "custom",
            "font": "var(--font-body--family)",
            "font_size": "2rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground-heading)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": "text__headingWrapper"
          },
          "blocks": {}
        },
        "group_qMd47g": {
          "type": "group",
          "name": "t:names.group",
          "disabled": true,
          "settings": {
            "link": "",
            "group-class": "",
            "open_in_new_tab": false,
            "content_direction": "row",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "center",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-start",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 12,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "toggle_overlay": false,
            "overlay_color": "#00000026",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "text_6kyt7E": {
              "type": "text",
              "name": "t:names.text",
              "settings": {
                "text": "<p>מאחורי המדע</p>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "1rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "icon_iz3nwL": {
              "type": "icon",
              "name": "t:names.icon",
              "settings": {
                "icon": "price_tag",
                "image_upload": "shopify://shop_images/arr.png",
                "width": 30,
                "link": "",
                "open_in_new_tab": false
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_6kyt7E",
            "icon_iz3nwL"
          ]
        }
      },
      "block_order": [
        "text_bWqt3X",
        "group_qMd47g"
      ],
      "custom_css": [
        "{direction: rtl; padding: 0 120px;}",
        "@media screen and (max-width: 767px) {{padding: 0 16px; } .pull__quoteTextWrapper > .custom-section-content > .section-content-wrapper {padding: 15px 0 40px; } .text__headingWrapper {padding: 0; } .text__headingWrapper p {font-size: 24px; } .group-block-content {flex-flow: row nowrap; justify-content: flex-start; display: none; }}"
      ],
      "name": "Split Showcase Header",
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-end",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "flex-end",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 0,
        "section_width": "full-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "scheme-794685bf-6ab5-4147-93f6-2ef40a4b25c2",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 40,
        "padding-block-end": 40,
        "custom_css_class": "pull__quoteTextWrapper"
      }
    },
    "section_4DT7JJ": {
      "type": "section",
      "blocks": {
        "image_fi3iaD": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/team_page_02.jpg",
            "link": "",
            "image_ratio": "adapt",
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fill",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 12,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "group_TLHReJ": {
          "type": "group",
          "settings": {
            "link": "",
            "group-class": "",
            "open_in_new_tab": false,
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "center",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-end",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 12,
            "width": "custom",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "toggle_overlay": false,
            "overlay_color": "#00000026",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "text_TybHcj": {
              "type": "text",
              "name": "t:names.heading",
              "settings": {
                "text": "<h3><strong>עמידה בלתי מתפשרת בקריטריונים הרגולטוריים המחמירים ביותר</strong></h3>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "2rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "text_URFU4w": {
              "type": "text",
              "settings": {
                "text": "<p>במעבדות הפיתוח של סקויה, כל תהליך מתבצע תחת פרוטוקולים מבוססי ראיות, התאמה לדרישות GMP, ובקרה מחמירה לפי נהלים בינלאומיים נוקשים. <br/>ייצור תוספי התזונה הליפוזומליים של סקויה נעשה בעזרת צוות מומחה בשלל תחומים מדעיים והנדסיים תוך כדי שימוש בציוד אנליטי מתקדם. התוצאה היא תוספים שמוכיחים את עצמם מבחינה קלינית, רגולטורית ותזונתית. </p>",
                "width": "fit-content",
                "max_width": "narrow",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "1rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "button_hAKBnG": {
              "type": "button",
              "disabled": true,
              "settings": {
                "label": "Shop now",
                "link": "shopify://collections/all",
                "open_in_new_tab": false,
                "style_class": "button",
                "width": "fit-content",
                "custom_width": 100,
                "width_mobile": "fit-content",
                "custom_width_mobile": 100,
                "custom_css_class": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_TybHcj",
            "text_URFU4w",
            "button_hAKBnG"
          ]
        }
      },
      "block_order": [
        "image_fi3iaD",
        "group_TLHReJ"
      ],
      "custom_css": [
        "{padding: 0 120px; border-radius: 12px 12px 0 0; overflow: hidden; position: relative; z-index: 2; margin-top: -40px;}",
        "@media screen and (max-width: 767px) {{padding: 0 16px; } h3 {font-size: 24px; }}",
        "p {direction: rtl; max-width: 100%;}"
      ],
      "name": "t:names.image_with_text",
      "settings": {
        "content_direction": "row",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "flex-start",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 82,
        "section_width": "full-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "scheme-794685bf-6ab5-4147-93f6-2ef40a4b25c2",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 60,
        "padding-block-end": 100,
        "custom_css_class": ""
      }
    },
    "section_UxdcKA": {
      "type": "section",
      "blocks": {
        "group_6PCTfT": {
          "type": "group",
          "settings": {
            "link": "",
            "group-class": "",
            "open_in_new_tab": false,
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "center",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-end",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 12,
            "width": "custom",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "toggle_overlay": false,
            "overlay_color": "#00000026",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "text_ktXTzf": {
              "type": "text",
              "name": "t:names.heading",
              "settings": {
                "text": "<h3><strong>איכות בלתי מתפשרת תחת רגולציה מלאה</strong></h3>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "2rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "text_dyJdQg": {
              "type": "text",
              "settings": {
                "text": "<p>המוצרים של סקויה מפותחים ומיוצרים בהתאם לתקני GMP מחמירים ובפיקוח משרד הבריאות, תוך בקרה מלאה על תהליכי הניסוח, הייצור והבדיקות.</p><p>באמצעות טכנולוגיה ליפוזומלית מתקדמת, ציוד מדעי מדויק ומחויבות לסטנדרטים הגבוהים ביותר, אנו מבטיחים שכל מוצר עומד בדרישות החוק ומעבר להן.</p>",
                "width": "fit-content",
                "max_width": "narrow",
                "alignment": "left",
                "type_preset": "custom",
                "font": "var(--font-body--family)",
                "font_size": "1rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0,
                "custom_css_class": ""
              },
              "blocks": {}
            },
            "button_c7PGJM": {
              "type": "button",
              "disabled": true,
              "settings": {
                "label": "Shop now",
                "link": "shopify://collections/all",
                "open_in_new_tab": false,
                "style_class": "button",
                "width": "fit-content",
                "custom_width": 100,
                "width_mobile": "fit-content",
                "custom_width_mobile": 100,
                "custom_css_class": ""
              },
              "blocks": {}
            }
          },
          "block_order": [
            "text_ktXTzf",
            "text_dyJdQg",
            "button_c7PGJM"
          ]
        },
        "image_exH4dr": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/team_page_03.jpg",
            "link": "",
            "image_ratio": "adapt",
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fill",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 12,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        }
      },
      "block_order": [
        "group_6PCTfT",
        "image_exH4dr"
      ],
      "custom_css": [
        "{padding: 0 120px; border-radius: 12px 12px 0 0; overflow: hidden; position: relative; z-index: 2; margin-top: -40px;}",
        "@media screen and (max-width: 767px) {{padding: 0 16px; } h3 {font-size: 24px; } .group-block {order: 1; }}",
        "p {direction: rtl; max-width: 100%;}"
      ],
      "name": "t:names.image_with_text",
      "settings": {
        "content_direction": "row",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "flex-start",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 82,
        "section_width": "full-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "scheme-794685bf-6ab5-4147-93f6-2ef40a4b25c2",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 60,
        "padding-block-end": 100,
        "custom_css_class": ""
      }
    }
  },
  "order": [
    "about_hero_banner_fBiE8y",
    "section_fPJFH6",
    "sp_our_stats_count_6w7Jh7",
    "scientists_experts_new",
    "section_AqrUGQ",
    "section_BqY9HD",
    "section_d8rLGf",
    "custom_info_blocks_VkGgkD",
    "sp_animated_boxes_xtE3gM",
    "animated_cards_4fDwfq",
    "section_QRD9Rx",
    "section_4DT7JJ",
    "section_UxdcKA"
  ]
}
