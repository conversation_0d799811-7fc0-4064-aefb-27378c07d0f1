{%- doc -%}
  Displays product images in a carousel.
  Settings allow for a full slideshow, showing only the first image, or showing the second image when hovering.
  Note: When the product card itself is in a carousel layout, the card-gallery's carousel is disabled with JavaScript.

  @param [children] - Additional content rendered below the card gallery
  @param [section] - The section object the snippet is rendered in
  @param [block] - The block object the snippet is rendered in
{%- enddoc -%}

{% liquid
  assign image_sizes = '(min-width: 750px) 50vw, 100vw'
  # if the card-gallery has a section.settings.product_card_size:
  # assume grid-template autofill(card-size, 1fr) and calculate the sizes attribute based on the minimum card size

  # if section has section.settings.columns:
  # assume grid-template repeat(column-count, 1fr) and calculate the sizes attribute based on the number of columns
%}

{% if section.settings.product_card_size %}
  {% capture card_size %}
    {% render 'util-product-grid-card-size' section: section %}
  {% endcapture %}
  {% assign card_size = card_size | strip | replace: 'px', '' | plus: 0 %}
  {% capture sizes_attribute %}
    {% render 'util-autofill-img-size-attr' card_size: card_size, card_gap: section.settings.columns_gap_horizontal %}
  {% endcapture %}
  {% assign image_sizes = sizes_attribute | strip %}
{% elsif section.settings.columns and section.settings.layout_type != 'editorial' %}
  {% assign viewport_width = 100.0 | divided_by: section.settings.columns %}
  {% assign sizes_attribute = '(min-width: 750px) [viewport_width]vw, 100vw'
    | replace: '[viewport_width]', viewport_width
  %}
  {% assign image_sizes = sizes_attribute | strip %}
{% endif %}

{% liquid
  assign product = block.settings.product

  assign lazy_image_sizes = 'auto, ' | append: image_sizes

  assign image_ratio_setting = block.settings.image_ratio
  assign temp_ratio = 1

  if image_ratio_setting == 'landscape'
    assign temp_ratio = '16 / 9'
  elsif image_ratio_setting == 'portrait'
    assign temp_ratio = '4 / 5'
  elsif image_ratio_setting == 'square'
    assign temp_ratio = '1'
  elsif image_ratio_setting == 'adapt'
    if product != blank
      assign current_featured_image_for_ratio = product.featured_image
      if current_featured_image_for_ratio == blank and product.featured_media.preview_image != blank
        assign current_featured_image_for_ratio = product.featured_media.preview_image
      endif

      if current_featured_image_for_ratio != blank and current_featured_image_for_ratio.aspect_ratio != null and current_featured_image_for_ratio.aspect_ratio > 0
        assign temp_ratio = current_featured_image_for_ratio.aspect_ratio
      endif
    endif
  endif

  if temp_ratio != blank and temp_ratio != 0 and temp_ratio != ''
    assign ratio = temp_ratio
  else
    assign ratio = 1
  endif

  assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src'
  assign selected_variant_image = product.selected_or_first_available_variant.image.src
  assign selected_variant_image_index = 0
  for image in product.images
    if image.src == selected_variant_image
      assign selected_variant_image_index = forloop.index0
    endif
  endfor

  assign hover_behavior = 'carousel'

  if block.settings.image_ratio == 'adapt' and block.settings.constrain_to_viewport
    if block.settings.constrain_to_viewport != ''
      assign constrain_to_viewport = true
      assign media_fit = block.settings.constrain_to_viewport
    endif
  endif
%}

{% capture placeholder_image %}
  <placeholder-image data-block-id="{{ section.id }}-{{ block.id }}" data-type="product"></placeholder-image>
{% endcapture %}
{% capture placeholder_slide %}
  {% render 'slideshow-slide', index: 1, children: placeholder_image, class: 'product-media-container card-gallery__placeholder' %}
{% endcapture %}
{% capture slideshow_placeholder %}
  {% render 'slideshow', ref: 'slideshow', slides: placeholder_slide, slide_count: 1, attributes: 'disabled="true"' %}
{% endcapture %}

<div
  ref="cardGallery"
  class="
    card-gallery card-gallery-{{ block.id }} spacing-style border-style
    {% if block.settings.product == blank and request.design_mode == false %}hidden{% endif %}
  "
  style="
    {% render 'border-override', settings: block.settings %}
    {% render 'spacing-style', settings: block.settings %}
    --gallery-aspect-ratio: {{ ratio }};
  "
  data-product-id="{{ product.id }}"
  {% comment %} Disabled hover preview to allow manual slideshow navigation {% endcomment %}
  {% comment %} on:pointerenter="/previewImage" {% endcomment %}
  {% comment %} on:pointerleave="/resetImage" {% endcomment %}
  {% if settings.transition_to_main_product %}
    data-view-transition-to-main-product
  {% endif %}
  data-image-ratio="{{ block.settings.image_ratio }}"
  {{ block.shopify_attributes }}
>
  {% comment %} Filter out the second image (index 1) from product media {% endcomment %}
  {% assign all_media = product.media %}

  {% comment %}
    For combined listings, also make sure to include the featured media of the color variants so that the swatch picker
    works as expected.
  {% endcomment %}
  {% for product_option in product.options_with_values %}
    {% for product_option_value in product_option.values %}
      {% if product_option_value.swatch %}
        {% assign featured_media = product_option_value.variant.featured_media %}

        {% comment %}
          If the variant has no featured media, and we have a combined listing product, then fall back to using the
          featured media of the child product that is linked to this option value.
        {% endcomment %}
        {% if featured_media == blank and product_option_value.product_url %}
          {% assign featured_media = product_option_value.variant.product.featured_media %}
        {% endif %}

        {% assign existing_media = all_media | has: 'id', featured_media.id %}
        {% unless existing_media %}
          {% assign variant_featured_media = product_option_value.variant.product.media
            | where: 'id', featured_media.id
          %}
          {% assign all_media = all_media | concat: variant_featured_media %}
        {% endunless %}
      {% endif %}
    {% endfor %}
  {% endfor %}

  {%- if all_media.size > 0 -%}
    {% comment %} Calculate actual slide count (excluding second image) {% endcomment %}
    {% assign actual_slide_count = all_media.size | minus: 1 %}
    {% if all_media.size < 2 %}
      {% assign actual_slide_count = all_media.size %}
    {% endif %}

    {% comment %} Show arrows for desktop, dots for mobile {% endcomment %}
    {% if actual_slide_count > 2 %}
      {% assign show_arrows = true %}

      {% comment %} Mobile dots controls {% endcomment %}
      {% capture mobile_controls %}
        {%- render 'slideshow-controls',
          style: 'dots',
          item_count: actual_slide_count,
          show_arrows: false,
          controls_on_media: true,
          pagination_position: 'center',
          class: 'mobile:block desktop:hidden'
        -%}
      {% endcapture %}
    {% else %}
      {% assign show_arrows = false %}
    {% endif %}

    {% capture slides %}
      {% assign slide_index = 0 %}
      {% for media in all_media %}
        {% comment %} Skip the second image (index 2) {% endcomment %}
        {% if forloop.index == 2 %}
          {% continue %}
        {% endif %}

        {% assign slide_index = slide_index | plus: 1 %}

        {% comment %} First image loads eagerly, rest load lazily for performance {% endcomment %}
        {% assign loading = 'lazy' %}
        {% assign sizes = lazy_image_sizes %}

        {% if slide_index == 1 and section.index == nil or section.index < 5 %}
          {% assign loading = 'eager' %}
          {% assign sizes = image_sizes %}
        {% endif %}

        {% assign attributes = '' %}
        {% capture slideshow_children %}
          {%- render 'product-media', media: media, sizes: sizes, loading: loading, preview_image_only: true -%}
        {% endcapture %}
        {% capture class %}
          product-media-container media-fit product-media-container--{{ media.media_type }}{% if constrain_to_viewport %} constrain-height{% endif %}
        {% endcapture %}
        {% assign hidden = false %}
        {% comment %} Don't hide variant images - show all images in slideshow {% endcomment %}
        {% if variant_images contains media.src %}
          {% assign attributes = 'variant-image' %}
        {% endif %}
        {% render 'slideshow-slide', slide_id: media.id, index: slide_index, children: slideshow_children, class: class, attributes: attributes, hidden: hidden, media_fit: media_fit %}
      {% endfor %}
    {% endcapture %}

    {% assign slideshow_attributes = '' %}
    {% if hover_behavior == 'none' %}
      {% assign slideshow_attributes = 'disabled="true"' %}
    {% endif %}

    {% comment %} Always start from first image {% endcomment %}
    {% assign initial_slide_index = 0 %}

    {% render 'slideshow',
      ref: 'slideshow',
      initial_slide: initial_slide_index,
      slides: slides,
      slide_count: actual_slide_count,
      show_arrows: show_arrows,
      arrows_position: 'bottom',
      icon_style: 'arrow',
      icon_shape: 'circle',
      controls: mobile_controls,
      attributes: slideshow_attributes,
      infinite: true
    %}
  {% elsif product != blank and product.media.size == 0 %}
    <product-title class="product-card-gallery__title-placeholder">
      <span class="title-text">{{- product.title -}}</span>
    </product-title>
  {% elsif product == blank %}
    {{ slideshow_placeholder }}
  {%- endif -%}

  {% comment %} Add to Cart Button in top-left corner {% endcomment %}
  {% unless product == blank %}
    <button
      type="button"
      class="card-gallery__add-to-cart-btn"
      data-product-id="{{ product.id }}"
      data-variant-id="{{ product.selected_or_first_available_variant.id }}"
      onclick="addToCartFromCard(this)"
      aria-label="הוסף לעגלה - {{ product.title }}"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 25 24" fill="none">
        <path d="M5.77548 19.3189L5.34777 19.935H5.34777L5.77548 19.3189ZM5.2354 12.2017L4.51447 11.995H4.51447L5.2354 12.2017ZM4.4308 17.6206L3.73128 17.891L3.73128 17.891L4.4308 17.6206ZM19.4302 12.2017L18.7093 12.4085L19.4302 12.2017ZM20.2348 17.6206L19.5353 17.3501L20.2348 17.6206ZM18.8901 19.3189L19.3179 19.935H19.3179L18.8901 19.3189ZM11.4824 10.4C11.0682 10.4 10.7324 10.7358 10.7324 11.15C10.7324 11.5642 11.0682 11.9 11.4824 11.9V11.15V10.4ZM13.1824 11.9C13.5966 11.9 13.9324 11.5642 13.9324 11.15C13.9324 10.7358 13.5966 10.4 13.1824 10.4V11.15V11.9ZM8.50781 8.6H9.25781V7.20909H8.50781H7.75781V8.6H8.50781ZM16.1578 7.20909H15.4078V8.6H16.1578H16.9078V7.20909H16.1578ZM12.3328 3.5V4.25C14.053 4.25 15.4078 5.59642 15.4078 7.20909H16.1578H16.9078C16.9078 4.72482 14.8376 2.75 12.3328 2.75V3.5ZM8.50781 7.20909H9.25781C9.25781 5.59642 10.6126 4.25 12.3328 4.25V3.5V2.75C9.82803 2.75 7.75781 4.72482 7.75781 7.20909H8.50781ZM10.7335 8.17505V8.92505H13.9322V8.17505V7.42505H10.7335V8.17505ZM5.2354 12.2017L4.51447 11.995C4.10733 13.4144 3.78494 14.535 3.62449 15.4392C3.46273 16.3507 3.44385 17.1477 3.73128 17.891L4.4308 17.6206L5.13032 17.3501C4.98735 16.9803 4.95911 16.5032 5.10141 15.7012C5.24503 14.8919 5.54014 13.8596 5.95633 12.4085L5.2354 12.2017ZM5.77548 19.3189L6.20319 18.7028C5.71038 18.3607 5.33865 17.8888 5.13032 17.3501L4.4308 17.6206L3.73128 17.891C4.05012 18.7156 4.61435 19.4258 5.34777 19.935L5.77548 19.3189ZM19.4302 12.2017L18.7093 12.4085C19.1255 13.8596 19.4206 14.8919 19.5642 15.7012C19.7065 16.5032 19.6783 16.9803 19.5353 17.3501L20.2348 17.6206L20.9343 17.891C21.2218 17.1477 21.2029 16.3507 21.0411 15.4392C20.8807 14.535 20.5583 13.4144 20.1512 11.995L19.4302 12.2017ZM20.2348 17.6206L19.5353 17.3501C19.327 17.8888 18.9552 18.3607 18.4624 18.7028L18.8901 19.3189L19.3179 19.935C20.0513 19.4258 20.6155 18.7156 20.9343 17.891L20.2348 17.6206ZM10.7335 8.17505V7.42505C9.04502 7.42505 7.69676 7.63257 6.65022 8.38411C5.59506 9.14184 4.9862 10.3503 4.51447 11.995L5.2354 12.2017L5.95633 12.4085C6.39869 10.8663 6.88617 10.0614 7.52517 9.60249C8.1728 9.13742 9.11653 8.92505 10.7335 8.92505V8.17505ZM19.4302 12.2017L20.1512 11.995C19.7003 10.4231 19.1292 9.216 18.0921 8.44028C17.0579 7.66678 15.6979 7.42505 13.9322 7.42505V8.17505V8.92505C15.6087 8.92505 16.5588 9.16664 17.1937 9.64148C17.8256 10.1141 18.2824 10.9203 18.7093 12.4085L19.4302 12.2017ZM5.77548 19.3189L5.34777 19.935C6.04481 20.4189 7.13575 20.7327 8.30714 20.9339C9.50863 21.1403 10.9039 21.2454 12.2924 21.2499C13.6808 21.2544 15.0833 21.1584 16.2989 20.9542C17.4862 20.7547 18.5956 20.4364 19.3179 19.935L18.8901 19.3189L18.4624 18.7028C18.0253 19.0063 17.1929 19.2829 16.0503 19.4749C14.936 19.6621 13.6205 19.7542 12.2973 19.7499C10.9742 19.7456 9.66417 19.6451 8.56107 19.4556C7.42787 19.2609 6.61494 18.9887 6.20319 18.7028L5.77548 19.3189ZM11.4824 11.15V11.9H13.1824V11.15V10.4H11.4824V11.15Z" fill="currentColor"/>
      </svg>
    </button>
  {% endunless %}

  {{ children }}
</div>

{% stylesheet %}
  .card-gallery {
    overflow: hidden;
    container-type: inline-size; /* Make card-gallery a container */
    container-name: card-gallery-container; /* Optional: name the container */
    position: relative; /* For absolute positioning of add to cart button */
  }

  /* Hide arrows on mobile, show on desktop */
  @media screen and (width < 750px) {
    .card-gallery slideshow-arrows {
      display: none !important;
    }
  }

  /* Hide dots on desktop, show on mobile */
  @media screen and (width >= 750px) {
    .card-gallery slideshow-controls.mobile\:block {
      display: none !important;
    }
  }

  /* Pagination dots styling for card gallery - Mobile only */
  @media screen and (width < 750px) {
    .card-gallery slideshow-controls {
      top: -45px;
      position: relative;
      z-index: 100;
    }

    .card-gallery .slideshow-controls__dots {
      gap: 12px;
      padding: 0;
      margin: 0;
      background: transparent;
      mix-blend-mode: normal;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 22px;
    }

    .card-gallery .slideshow-controls__dots li {
      list-style: none;
      margin: 0;
      padding: 0;
      display: flex;
    }

    .card-gallery .slideshow-controls__dots button {
      width: 20px;
      height: 20px;
      min-width: 20px;
      min-height: 20px;
      border-radius: 50%;
      background-color: #2d2c2c !important;
      padding: 0;
      margin: 0;
      font-size: 0 !important;
      line-height: 0;
      transition: all 0.25s ease;
      border: none;
      cursor: pointer;
      display: block;
      overflow: hidden;
      text-indent: -9999px;
      color: transparent !important;
    }

    /* Hide inner content/numbers */
    .card-gallery .slideshow-controls__dots button::before,
    .card-gallery .slideshow-controls__dots button::after,
    .card-gallery .slideshow-controls__dots button * {
      display: none !important;
      content: none !important;
      visibility: hidden !important;
    }

    .card-gallery .slideshow-controls__dots button[aria-selected="true"] {
      background-color: #948e8e !important;
      transform: scale(1.1);
    }

    .card-gallery .slideshow-controls__dots button:active {
      background-color: #999999 !important;
    }

    /* Hide progress bar */
    .card-gallery .progress__barWrapper,
    .card-gallery .carousel__progressWrapper,
    .card-gallery .carousel__progressBar {
      display: none !important;
    }
  }

  /* Enable touch scrolling for mobile */
  @media screen and (width < 750px) {
    .card-gallery slideshow-slides {
      scroll-snap-type: x mandatory;
      -webkit-overflow-scrolling: touch;
      touch-action: pan-x pan-y;
      scroll-behavior: smooth;
      overscroll-behavior-x: contain;
    }

    /* Allow vertical scroll on page while horizontal swipe on slider */
    .card-gallery {
      touch-action: pan-y;
      overscroll-behavior-x: contain;
    }

    .card-gallery slideshow-component {
      touch-action: pan-x pan-y;
    }
  }

  /* Desktop optimizations */
  @media screen and (width >= 750px) {
    .card-gallery slideshow-slides {
      scroll-behavior: smooth;
    }
  }



  .card-gallery__add-to-cart-btn {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    border: 0px;
    border-radius: 22%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
  }

  .card-gallery__add-to-cart-btn:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #007A73;
    transform: scale(1.05);
  }

  .card-gallery__add-to-cart-btn svg {
    color: #323438;
    transition: color 0.2s ease;
  }

  .card-gallery__add-to-cart-btn:hover svg {
    color: #007A73;
  }

  .card-gallery__add-to-cart-btn:active {
    transform: scale(0.95);
  }

  .card-gallery__placeholder svg {
    height: 100%;
    width: 100%;
  }

  .card-gallery placeholder-image {
    aspect-ratio: var(--gallery-aspect-ratio, var(--ratio));
  }

  .product-card-gallery__title-placeholder {
    padding: var(--padding-md);
    font-size: var(--font-size--2xl);
    line-height: var(--line-height--display-loose);
    word-break: break-word;
    color: var(--color-foreground);
    background-color: rgb(from var(--color-foreground) r g b / 5%);
    aspect-ratio: var(--gallery-aspect-ratio);
    border-radius: var(--product-corner-radius);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  @media screen and (width >= 750px) {
    .product-grid[data-product-card-size='extra-large'] .product-card-gallery__title-placeholder {
      padding: var(--padding-3xl);
      font-size: var(--font-size--3xl);
    }

    .product-grid[data-product-card-size='large'] .product-card-gallery__title-placeholder {
      padding: var(--padding-2xl);
      font-size: var(--font-size--2xl);
    }

    .product-grid[data-product-card-size='medium'] .product-card-gallery__title-placeholder {
      padding: var(--padding-xl);
      font-size: var(--font-size--xl);
    }

    .product-grid[data-product-card-size='small'] .product-card-gallery__title-placeholder {
      padding: var(--padding-sm);
      font-size: var(--font-size--lg);
    }

    .product-grid[data-product-card-size='extra-large']
      .card-gallery:has(.product-badges--top-right .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-right: calc(var(--padding-3xl) + 50px);
    }

    .product-grid[data-product-card-size='large']
      .card-gallery:has(.product-badges--top-right .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-right: calc(var(--padding-2xl) + 50px);
    }

    .product-grid[data-product-card-size='medium']
      .card-gallery:has(.product-badges--top-right .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-right: calc(var(--padding-xl) + 50px);
    }

    .product-grid[data-product-card-size='small']
      .card-gallery:has(.product-badges--top-right .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-right: calc(var(--padding-sm) + 50px);
    }

    .product-grid[data-product-card-size='extra-large']
      .card-gallery:has(.product-badges--top-left .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-top: calc(var(--padding-3xl) + 40px);
    }

    .product-grid[data-product-card-size='large']
      .card-gallery:has(.product-badges--top-left .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-top: calc(var(--padding-2xl) + 40px);
    }

    .product-grid[data-product-card-size='medium']
      .card-gallery:has(.product-badges--top-left .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-top: calc(var(--padding-xl) + 40px);
    }

    .product-grid[data-product-card-size='small']
      .card-gallery:has(.product-badges--top-left .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-top: calc(var(--padding-sm) + 40px);
    }

    .product-grid[data-product-card-size='extra-large']
      .card-gallery:has(.product-badges--bottom-left .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-bottom: calc(var(--padding-3xl) + 40px);
    }

    .product-grid[data-product-card-size='large']
      .card-gallery:has(.product-badges--bottom-left .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-bottom: calc(var(--padding-2xl) + 40px);
    }

    .product-grid[data-product-card-size='medium']
      .card-gallery:has(.product-badges--bottom-left .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-bottom: calc(var(--padding-xl) + 40px);
    }

    .product-grid[data-product-card-size='small']
      .card-gallery:has(.product-badges--bottom-left .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-bottom: calc(var(--padding-sm) + 40px);
    }
  }

  @media screen and (width < 750px) {
    .product-card-gallery__title-placeholder {
      font-size: var(--font-size--xl);
      padding: var(--padding-md);
    }

    .product-grid[data-product-card-size]
      .card-gallery:has(.product-badges--top-right .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-right: calc(var(--padding-sm) + 50px);
    }

    .product-grid[data-product-card-size]
      .card-gallery:has(.product-badges--top-left .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-top: calc(var(--padding-sm) + 40px);
    }

    .product-grid[data-product-card-size]
      .card-gallery:has(.product-badges--bottom-left .product-badges__badge)
      .product-card-gallery__title-placeholder {
      padding-bottom: calc(var(--padding-sm) + 40px);
    }
  }

  [product-grid-view='zoom-out'] .card-gallery .product-card-gallery__title-placeholder {
    padding: var(--padding-xs) !important;
    font-size: var(--font-size--xs);
  }
{% endstylesheet %}

<script>
  // Optimize card gallery slideshows on page load
  document.addEventListener('DOMContentLoaded', function() {
    const cardGalleries = document.querySelectorAll('.card-gallery');

    cardGalleries.forEach(gallery => {
      const slideshow = gallery.querySelector('slideshow-component');
      if (!slideshow) return;

      // Disable autoplay for card galleries
      slideshow.removeAttribute('autoplay');

      // Add performance optimizations
      const slides = slideshow.querySelectorAll('slideshow-slide');
      slides.forEach((slide, index) => {
        // Only load first 2 images immediately
        if (index > 1) {
          const images = slide.querySelectorAll('img');
          images.forEach(img => {
            if (img.loading !== 'lazy') {
              img.loading = 'lazy';
            }
          });
        }
      });
    });
  });

  window.addToCartFromCard = function(button) {
    const variantId = button.getAttribute('data-variant-id');
    const productId = button.getAttribute('data-product-id');

    if (!variantId) {
      console.error('No variant ID found');
      return;
    }

    // Disable button during request
    button.disabled = true;
    button.style.opacity = '0.6';

    // Create FormData for the request
    const formData = new FormData();
    formData.append('id', variantId);
    formData.append('quantity', '1');

    // Get cart sections to update
    const cartItemsComponents = document.querySelectorAll('cart-items-component');
    let cartItemComponentsSectionIds = [];
    cartItemsComponents.forEach((item) => {
      if (item instanceof HTMLElement && item.dataset.sectionId) {
        cartItemComponentsSectionIds.push(item.dataset.sectionId);
      }
    });
    if (cartItemComponentsSectionIds.length > 0) {
      formData.append('sections', cartItemComponentsSectionIds.join(','));
    }

    // Add to cart using Shopify Cart API
    fetch(Theme.routes.cart_add_url, {
      method: 'POST',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'application/json'
      },
      body: formData
    })
    .then(response => response.json())
    .then(response => {
      if (response.status) {
        // Error occurred
        console.error('Cart add error:', response.message);
        showErrorFeedback(button);
        return;
      }

      // Success - dispatch cart update event to trigger drawer opening
      const cartUpdateEvent = new CustomEvent('cart:update', {
        bubbles: true,
        detail: {
          resource: response,
          sourceId: `card-gallery-${productId}`,
          data: {
            source: 'product-form-component', // This triggers drawer opening
            itemCount: response.item_count || 1,
            productId: productId,
            sections: response.sections || {},
            didError: false
          }
        }
      });

      document.dispatchEvent(cartUpdateEvent);

      // Also manually open cart drawer if it exists
      const cartDrawer = document.querySelector('cart-drawer-component');
      if (cartDrawer && cartDrawer.showDialog) {
        cartDrawer.showDialog();
      }

      // Success feedback
      showSuccessFeedback(button);
    })
    .catch(error => {
      console.error('Error adding to cart:', error);
      showErrorFeedback(button);
    });
  };

  function showSuccessFeedback(button) {
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M13.5 4.5L6 12L2.5 8.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;
    button.style.background = 'rgba(0, 122, 115, 0.1)';

    // Reset button after 2 seconds
    setTimeout(() => {
      resetButton(button);
    }, 2000);
  }

  function showErrorFeedback(button) {
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 4L4 12M4 4l8 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;
    button.style.background = 'rgba(220, 53, 69, 0.1)';

    // Reset button after 2 seconds
    setTimeout(() => {
      resetButton(button);
    }, 2000);
  }

  function resetButton(button) {
    button.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 25 24" fill="none">
        <path d="M5.77548 19.3189L5.34777 19.935H5.34777L5.77548 19.3189ZM5.2354 12.2017L4.51447 11.995H4.51447L5.2354 12.2017ZM4.4308 17.6206L3.73128 17.891L3.73128 17.891L4.4308 17.6206ZM19.4302 12.2017L18.7093 12.4085L19.4302 12.2017ZM20.2348 17.6206L19.5353 17.3501L20.2348 17.6206ZM18.8901 19.3189L19.3179 19.935H19.3179L18.8901 19.3189ZM11.4824 10.4C11.0682 10.4 10.7324 10.7358 10.7324 11.15C10.7324 11.5642 11.0682 11.9 11.4824 11.9V11.15V10.4ZM13.1824 11.9C13.5966 11.9 13.9324 11.5642 13.9324 11.15C13.9324 10.7358 13.5966 10.4 13.1824 10.4V11.15V11.9ZM8.50781 8.6H9.25781V7.20909H8.50781H7.75781V8.6H8.50781ZM16.1578 7.20909H15.4078V8.6H16.1578H16.9078V7.20909H16.1578ZM12.3328 3.5V4.25C14.053 4.25 15.4078 5.59642 15.4078 7.20909H16.1578H16.9078C16.9078 4.72482 14.8376 2.75 12.3328 2.75V3.5ZM8.50781 7.20909H9.25781C9.25781 5.59642 10.6126 4.25 12.3328 4.25V3.5V2.75C9.82803 2.75 7.75781 4.72482 7.75781 7.20909H8.50781ZM10.7335 8.17505V8.92505H13.9322V8.17505V7.42505H10.7335V8.17505ZM5.2354 12.2017L4.51447 11.995C4.10733 13.4144 3.78494 14.535 3.62449 15.4392C3.46273 16.3507 3.44385 17.1477 3.73128 17.891L4.4308 17.6206L5.13032 17.3501C4.98735 16.9803 4.95911 16.5032 5.10141 15.7012C5.24503 14.8919 5.54014 13.8596 5.95633 12.4085L5.2354 12.2017ZM5.77548 19.3189L6.20319 18.7028C5.71038 18.3607 5.33865 17.8888 5.13032 17.3501L4.4308 17.6206L3.73128 17.891C4.05012 18.7156 4.61435 19.4258 5.34777 19.935L5.77548 19.3189ZM19.4302 12.2017L18.7093 12.4085C19.1255 13.8596 19.4206 14.8919 19.5642 15.7012C19.7065 16.5032 19.6783 16.9803 19.5353 17.3501L20.2348 17.6206L20.9343 17.891C21.2218 17.1477 21.2029 16.3507 21.0411 15.4392C20.8807 14.535 20.5583 13.4144 20.1512 11.995L19.4302 12.2017ZM20.2348 17.6206L19.5353 17.3501C19.327 17.8888 18.9552 18.3607 18.4624 18.7028L18.8901 19.3189L19.3179 19.935C20.0513 19.4258 20.6155 18.7156 20.9343 17.891L20.2348 17.6206ZM10.7335 8.17505V7.42505C9.04502 7.42505 7.69676 7.63257 6.65022 8.38411C5.59506 9.14184 4.9862 10.3503 4.51447 11.995L5.2354 12.2017L5.95633 12.4085C6.39869 10.8663 6.88617 10.0614 7.52517 9.60249C8.1728 9.13742 9.11653 8.92505 10.7335 8.92505V8.17505ZM19.4302 12.2017L20.1512 11.995C19.7003 10.4231 19.1292 9.216 18.0921 8.44028C17.0579 7.66678 15.6979 7.42505 13.9322 7.42505V8.17505V8.92505C15.6087 8.92505 16.5588 9.16664 17.1937 9.64148C17.8256 10.1141 18.2824 10.9203 18.7093 12.4085L19.4302 12.2017ZM5.77548 19.3189L5.34777 19.935C6.04481 20.4189 7.13575 20.7327 8.30714 20.9339C9.50863 21.1403 10.9039 21.2454 12.2924 21.2499C13.6808 21.2544 15.0833 21.1584 16.2989 20.9542C17.4862 20.7547 18.5956 20.4364 19.3179 19.935L18.8901 19.3189L18.4624 18.7028C18.0253 19.0063 17.1929 19.2829 16.0503 19.4749C14.936 19.6621 13.6205 19.7542 12.2973 19.7499C10.9742 19.7456 9.66417 19.6451 8.56107 19.4556C7.42787 19.2609 6.61494 18.9887 6.20319 18.7028L5.77548 19.3189ZM11.4824 11.15V11.9H13.1824V11.15V10.4H11.4824V11.15Z" fill="currentColor"/>
      </svg>
    `;
    button.style.background = 'rgba(255, 255, 255, 0.9)';
    button.disabled = false;
    button.style.opacity = '1';
  }
</script>
